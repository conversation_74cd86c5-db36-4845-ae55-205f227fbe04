{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardActionAreaUtilityClass(slot) {\n  return generateUtilityClass('MuiCardActionArea', slot);\n}\nconst cardActionAreaClasses = generateUtilityClasses('MuiCardActionArea', ['root', 'focusVisible', 'focusHighlight']);\nexport default cardActionAreaClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getCardActionAreaUtilityClass", "slot", "cardActionAreaClasses"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/CardActionArea/cardActionAreaClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardActionAreaUtilityClass(slot) {\n  return generateUtilityClass('MuiCardActionArea', slot);\n}\nconst cardActionAreaClasses = generateUtilityClasses('MuiCardActionArea', ['root', 'focusVisible', 'focusHighlight']);\nexport default cardActionAreaClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOF,oBAAoB,CAAC,mBAAmB,EAAEE,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGJ,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;AACrH,eAAeI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}