{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getBottomNavigationUtilityClass(slot) {\n  return generateUtilityClass('MuiBottomNavigation', slot);\n}\nconst bottomNavigationClasses = generateUtilityClasses('MuiBottomNavigation', ['root']);\nexport default bottomNavigationClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getBottomNavigationUtilityClass", "slot", "bottomNavigationClasses"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/BottomNavigation/bottomNavigationClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getBottomNavigationUtilityClass(slot) {\n  return generateUtilityClass('MuiBottomNavigation', slot);\n}\nconst bottomNavigationClasses = generateUtilityClasses('MuiBottomNavigation', ['root']);\nexport default bottomNavigationClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,OAAOF,oBAAoB,CAAC,qBAAqB,EAAEE,IAAI,CAAC;AAC1D;AACA,MAAMC,uBAAuB,GAAGJ,sBAAsB,CAAC,qBAAqB,EAAE,CAAC,MAAM,CAAC,CAAC;AACvF,eAAeI,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}