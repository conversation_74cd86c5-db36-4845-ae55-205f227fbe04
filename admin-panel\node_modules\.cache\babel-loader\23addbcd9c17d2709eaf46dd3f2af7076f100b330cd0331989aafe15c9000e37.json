{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getMobileStepperUtilityClass(slot) {\n  return generateUtilityClass('MuiMobileStepper', slot);\n}\nconst mobileStepperClasses = generateUtilityClasses('MuiMobileStepper', ['root', 'positionBottom', 'positionTop', 'positionStatic', 'dots', 'dot', 'dotActive', 'progress']);\nexport default mobileStepperClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getMobileStepperUtilityClass", "slot", "mobileStepperClasses"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/MobileStepper/mobileStepperClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getMobileStepperUtilityClass(slot) {\n  return generateUtilityClass('MuiMobileStepper', slot);\n}\nconst mobileStepperClasses = generateUtilityClasses('MuiMobileStepper', ['root', 'positionBottom', 'positionTop', 'positionStatic', 'dots', 'dot', 'dotActive', 'progress']);\nexport default mobileStepperClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOF,oBAAoB,CAAC,kBAAkB,EAAEE,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGJ,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,gBAAgB,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;AAC5K,eAAeI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}