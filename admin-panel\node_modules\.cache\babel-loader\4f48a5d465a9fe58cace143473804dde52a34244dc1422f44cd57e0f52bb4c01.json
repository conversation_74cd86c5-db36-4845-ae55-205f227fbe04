{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Collapse from '../Collapse';\nimport StepperContext from '../Stepper/StepperContext';\nimport StepContext from '../Step/StepContext';\nimport { getStepContentUtilityClass } from './stepContentClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    last\n  } = ownerState;\n  const slots = {\n    root: ['root', last && 'last'],\n    transition: ['transition']\n  };\n  return composeClasses(slots, getStepContentUtilityClass, classes);\n};\nconst StepContentRoot = styled('div', {\n  name: 'MuiStepContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.last && styles.last];\n  }\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  marginLeft: 12,\n  // half icon\n  paddingLeft: 8 + 12,\n  // margin + half icon\n  paddingRight: 8,\n  borderLeft: theme.vars ? `1px solid ${theme.vars.palette.StepContent.border}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[600]}`\n}, ownerState.last && {\n  borderLeft: 'none'\n}));\nconst StepContentTransition = styled(Collapse, {\n  name: 'MuiStepContent',\n  slot: 'Transition',\n  overridesResolver: (props, styles) => styles.transition\n})({});\nconst StepContent = /*#__PURE__*/React.forwardRef(function StepContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepContent'\n  });\n  const {\n      children,\n      className,\n      TransitionComponent = Collapse,\n      transitionDuration: transitionDurationProp = 'auto',\n      TransitionProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    orientation\n  } = React.useContext(StepperContext);\n  const {\n    active,\n    last,\n    expanded\n  } = React.useContext(StepContext);\n  const ownerState = _extends({}, props, {\n    last\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    if (orientation !== 'vertical') {\n      console.error('MUI: <StepContent /> is only designed for use with the vertical stepper.');\n    }\n  }\n  let transitionDuration = transitionDurationProp;\n  if (transitionDurationProp === 'auto' && !TransitionComponent.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n  return /*#__PURE__*/_jsx(StepContentRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(StepContentTransition, _extends({\n      as: TransitionComponent,\n      in: active || expanded,\n      className: classes.transition,\n      ownerState: ownerState,\n      timeout: transitionDuration,\n      unmountOnExit: true\n    }, TransitionProps, {\n      children: children\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? StepContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Collapse\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Adjust the duration of the content expand transition.\n   * Passed as a prop to the transition component.\n   *\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default StepContent;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "Collapse", "StepperContext", "StepContext", "getStepContentUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "last", "slots", "root", "transition", "StepContentRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "marginLeft", "paddingLeft", "paddingRight", "borderLeft", "vars", "palette", "<PERSON><PERSON><PERSON><PERSON>", "border", "mode", "grey", "StepContentTransition", "forwardRef", "inProps", "ref", "children", "className", "TransitionComponent", "transitionDuration", "transitionDurationProp", "TransitionProps", "other", "orientation", "useContext", "active", "expanded", "process", "env", "NODE_ENV", "console", "error", "muiSupportAuto", "undefined", "as", "in", "timeout", "unmountOnExit", "propTypes", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool", "elementType", "oneOf", "number", "shape", "appear", "enter", "exit"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/StepContent/StepContent.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Collapse from '../Collapse';\nimport StepperContext from '../Stepper/StepperContext';\nimport StepContext from '../Step/StepContext';\nimport { getStepContentUtilityClass } from './stepContentClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    last\n  } = ownerState;\n  const slots = {\n    root: ['root', last && 'last'],\n    transition: ['transition']\n  };\n  return composeClasses(slots, getStepContentUtilityClass, classes);\n};\nconst StepContentRoot = styled('div', {\n  name: 'MuiStepContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.last && styles.last];\n  }\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  marginLeft: 12,\n  // half icon\n  paddingLeft: 8 + 12,\n  // margin + half icon\n  paddingRight: 8,\n  borderLeft: theme.vars ? `1px solid ${theme.vars.palette.StepContent.border}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[600]}`\n}, ownerState.last && {\n  borderLeft: 'none'\n}));\nconst StepContentTransition = styled(Collapse, {\n  name: 'MuiStepContent',\n  slot: 'Transition',\n  overridesResolver: (props, styles) => styles.transition\n})({});\nconst StepContent = /*#__PURE__*/React.forwardRef(function StepContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepContent'\n  });\n  const {\n      children,\n      className,\n      TransitionComponent = Collapse,\n      transitionDuration: transitionDurationProp = 'auto',\n      TransitionProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    orientation\n  } = React.useContext(StepperContext);\n  const {\n    active,\n    last,\n    expanded\n  } = React.useContext(StepContext);\n  const ownerState = _extends({}, props, {\n    last\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    if (orientation !== 'vertical') {\n      console.error('MUI: <StepContent /> is only designed for use with the vertical stepper.');\n    }\n  }\n  let transitionDuration = transitionDurationProp;\n  if (transitionDurationProp === 'auto' && !TransitionComponent.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n  return /*#__PURE__*/_jsx(StepContentRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(StepContentTransition, _extends({\n      as: TransitionComponent,\n      in: active || expanded,\n      className: classes.transition,\n      ownerState: ownerState,\n      timeout: transitionDuration,\n      unmountOnExit: true\n    }, TransitionProps, {\n      children: children\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? StepContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Collapse\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Adjust the duration of the content expand transition.\n   * Passed as a prop to the transition component.\n   *\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default StepContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;AAC3G,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,IAAI,IAAI,MAAM,CAAC;IAC9BG,UAAU,EAAE,CAAC,YAAY;EAC3B,CAAC;EACD,OAAOf,cAAc,CAACa,KAAK,EAAEP,0BAA0B,EAAEK,OAAO,CAAC;AACnE,CAAC;AACD,MAAMK,eAAe,GAAGf,MAAM,CAAC,KAAK,EAAE;EACpCgB,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,IAAI,EAAEJ,UAAU,CAACE,IAAI,IAAIS,MAAM,CAACT,IAAI,CAAC;EACtD;AACF,CAAC,CAAC,CAAC,CAAC;EACFF,UAAU;EACVY;AACF,CAAC,KAAK3B,QAAQ,CAAC;EACb4B,UAAU,EAAE,EAAE;EACd;EACAC,WAAW,EAAE,CAAC,GAAG,EAAE;EACnB;EACAC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAEJ,KAAK,CAACK,IAAI,GAAG,aAAaL,KAAK,CAACK,IAAI,CAACC,OAAO,CAACC,WAAW,CAACC,MAAM,EAAE,GAAG,aAAaR,KAAK,CAACM,OAAO,CAACG,IAAI,KAAK,OAAO,GAAGT,KAAK,CAACM,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,GAAGV,KAAK,CAACM,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC;AACjL,CAAC,EAAEtB,UAAU,CAACE,IAAI,IAAI;EACpBc,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;AACH,MAAMO,qBAAqB,GAAGhC,MAAM,CAACE,QAAQ,EAAE;EAC7Cc,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,YAAY;EAClBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,MAAMc,WAAW,GAAG,aAAahC,KAAK,CAACqC,UAAU,CAAC,SAASL,WAAWA,CAACM,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAMhB,KAAK,GAAGlB,eAAe,CAAC;IAC5BkB,KAAK,EAAEe,OAAO;IACdlB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFoB,QAAQ;MACRC,SAAS;MACTC,mBAAmB,GAAGpC,QAAQ;MAC9BqC,kBAAkB,EAAEC,sBAAsB,GAAG,MAAM;MACnDC;IACF,CAAC,GAAGtB,KAAK;IACTuB,KAAK,GAAGjD,6BAA6B,CAAC0B,KAAK,EAAExB,SAAS,CAAC;EACzD,MAAM;IACJgD;EACF,CAAC,GAAG/C,KAAK,CAACgD,UAAU,CAACzC,cAAc,CAAC;EACpC,MAAM;IACJ0C,MAAM;IACNlC,IAAI;IACJmC;EACF,CAAC,GAAGlD,KAAK,CAACgD,UAAU,CAACxC,WAAW,CAAC;EACjC,MAAMK,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;IACrCR;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,IAAIsC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIN,WAAW,KAAK,UAAU,EAAE;MAC9BO,OAAO,CAACC,KAAK,CAAC,0EAA0E,CAAC;IAC3F;EACF;EACA,IAAIZ,kBAAkB,GAAGC,sBAAsB;EAC/C,IAAIA,sBAAsB,KAAK,MAAM,IAAI,CAACF,mBAAmB,CAACc,cAAc,EAAE;IAC5Eb,kBAAkB,GAAGc,SAAS;EAChC;EACA,OAAO,aAAa9C,IAAI,CAACQ,eAAe,EAAErB,QAAQ,CAAC;IACjD2C,SAAS,EAAEvC,IAAI,CAACY,OAAO,CAACG,IAAI,EAAEwB,SAAS,CAAC;IACxCF,GAAG,EAAEA,GAAG;IACR1B,UAAU,EAAEA;EACd,CAAC,EAAEiC,KAAK,EAAE;IACRN,QAAQ,EAAE,aAAa7B,IAAI,CAACyB,qBAAqB,EAAEtC,QAAQ,CAAC;MAC1D4D,EAAE,EAAEhB,mBAAmB;MACvBiB,EAAE,EAAEV,MAAM,IAAIC,QAAQ;MACtBT,SAAS,EAAE3B,OAAO,CAACI,UAAU;MAC7BL,UAAU,EAAEA,UAAU;MACtB+C,OAAO,EAAEjB,kBAAkB;MAC3BkB,aAAa,EAAE;IACjB,CAAC,EAAEhB,eAAe,EAAE;MAClBL,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrB,WAAW,CAAC8B,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEtB,QAAQ,EAAEvC,SAAS,CAAC8D,IAAI;EACxB;AACF;AACA;EACEjD,OAAO,EAAEb,SAAS,CAAC+D,MAAM;EACzB;AACF;AACA;EACEvB,SAAS,EAAExC,SAAS,CAACgE,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAEjE,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACmE,OAAO,CAACnE,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAAC+D,MAAM,EAAE/D,SAAS,CAACqE,IAAI,CAAC,CAAC,CAAC,EAAErE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAAC+D,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;EACEtB,mBAAmB,EAAEzC,SAAS,CAACsE,WAAW;EAC1C;AACF;AACA;AACA;AACA;AACA;AACA;EACE5B,kBAAkB,EAAE1C,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACuE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAEvE,SAAS,CAACwE,MAAM,EAAExE,SAAS,CAACyE,KAAK,CAAC;IACpGC,MAAM,EAAE1E,SAAS,CAACwE,MAAM;IACxBG,KAAK,EAAE3E,SAAS,CAACwE,MAAM;IACvBI,IAAI,EAAE5E,SAAS,CAACwE;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;EACE5B,eAAe,EAAE5C,SAAS,CAAC+D;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAehC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}