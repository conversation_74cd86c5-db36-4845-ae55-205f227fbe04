{"ast": null, "code": "export { default } from './composeClasses';", "map": {"version": 3, "names": ["default"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/utils/esm/composeClasses/index.js"], "sourcesContent": ["export { default } from './composeClasses';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}