{"ast": null, "code": "'use strict';\n\nvar DESCRIPTORS = require('../internals/descriptors');\nvar call = require('../internals/function-call');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPropertyKey = require('../internals/to-property-key');\nvar hasOwn = require('../internals/has-own-property');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) {/* empty */}\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "call", "propertyIsEnumerableModule", "createPropertyDescriptor", "toIndexedObject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwn", "IE8_DOM_DEFINE", "$getOwnPropertyDescriptor", "Object", "getOwnPropertyDescriptor", "exports", "f", "O", "P", "error"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/core-js-pure/internals/object-get-own-property-descriptor.js"], "sourcesContent": ["'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar call = require('../internals/function-call');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPropertyKey = require('../internals/to-property-key');\nvar hasOwn = require('../internals/has-own-property');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACrD,IAAIC,IAAI,GAAGD,OAAO,CAAC,4BAA4B,CAAC;AAChD,IAAIE,0BAA0B,GAAGF,OAAO,CAAC,4CAA4C,CAAC;AACtF,IAAIG,wBAAwB,GAAGH,OAAO,CAAC,yCAAyC,CAAC;AACjF,IAAII,eAAe,GAAGJ,OAAO,CAAC,gCAAgC,CAAC;AAC/D,IAAIK,aAAa,GAAGL,OAAO,CAAC,8BAA8B,CAAC;AAC3D,IAAIM,MAAM,GAAGN,OAAO,CAAC,+BAA+B,CAAC;AACrD,IAAIO,cAAc,GAAGP,OAAO,CAAC,6BAA6B,CAAC;;AAE3D;AACA,IAAIQ,yBAAyB,GAAGC,MAAM,CAACC,wBAAwB;;AAE/D;AACA;AACAC,OAAO,CAACC,CAAC,GAAGb,WAAW,GAAGS,yBAAyB,GAAG,SAASE,wBAAwBA,CAACG,CAAC,EAAEC,CAAC,EAAE;EAC5FD,CAAC,GAAGT,eAAe,CAACS,CAAC,CAAC;EACtBC,CAAC,GAAGT,aAAa,CAACS,CAAC,CAAC;EACpB,IAAIP,cAAc,EAAE,IAAI;IACtB,OAAOC,yBAAyB,CAACK,CAAC,EAAEC,CAAC,CAAC;EACxC,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAE;EAClB,IAAIT,MAAM,CAACO,CAAC,EAAEC,CAAC,CAAC,EAAE,OAAOX,wBAAwB,CAAC,CAACF,IAAI,CAACC,0BAA0B,CAACU,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC;AACpG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}