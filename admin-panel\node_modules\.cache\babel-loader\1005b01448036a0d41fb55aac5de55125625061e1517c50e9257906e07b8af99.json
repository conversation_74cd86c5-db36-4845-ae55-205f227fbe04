{"ast": null, "code": "export { default } from './requirePropFactory';", "map": {"version": 3, "names": ["default"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/utils/esm/requirePropFactory/index.js"], "sourcesContent": ["export { default } from './requirePropFactory';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}