{"ast": null, "code": "import isMuiElement from '@mui/utils/isMuiElement';\nexport default isMuiElement;", "map": {"version": 3, "names": ["isMuiElement"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/utils/isMuiElement.js"], "sourcesContent": ["import isMuiElement from '@mui/utils/isMuiElement';\nexport default isMuiElement;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,yBAAyB;AAClD,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}