{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"aria-label\", \"aria-labelledby\", \"action\", \"centered\", \"children\", \"className\", \"component\", \"allowScrollButtonsMobile\", \"indicatorColor\", \"onChange\", \"orientation\", \"ScrollButtonComponent\", \"scrollButtons\", \"selectionFollowsFocus\", \"slots\", \"slotProps\", \"TabIndicatorProps\", \"TabScrollButtonProps\", \"textColor\", \"value\", \"variant\", \"visibleScrollbar\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport debounce from '../utils/debounce';\nimport { getNormalizedScrollLeft, detectScrollType } from '../utils/scrollLeft';\nimport animate from '../internal/animate';\nimport ScrollbarSize from './ScrollbarSize';\nimport TabScrollButton from '../TabScrollButton';\nimport useEventCallback from '../utils/useEventCallback';\nimport tabsClasses, { getTabsUtilityClass } from './tabsClasses';\nimport ownerDocument from '../utils/ownerDocument';\nimport ownerWindow from '../utils/ownerWindow';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst nextItem = (list, item) => {\n  if (list === item) {\n    return list.firstChild;\n  }\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n  return list.firstChild;\n};\nconst previousItem = (list, item) => {\n  if (list === item) {\n    return list.lastChild;\n  }\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n  return list.lastChild;\n};\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n  while (nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n      wrappedOnce = true;\n    }\n\n    // Same logic as useAutocomplete.js\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    vertical,\n    fixed,\n    hideScrollbar,\n    scrollableX,\n    scrollableY,\n    centered,\n    scrollButtonsHideMobile,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', vertical && 'vertical'],\n    scroller: ['scroller', fixed && 'fixed', hideScrollbar && 'hideScrollbar', scrollableX && 'scrollableX', scrollableY && 'scrollableY'],\n    flexContainer: ['flexContainer', vertical && 'flexContainerVertical', centered && 'centered'],\n    indicator: ['indicator'],\n    scrollButtons: ['scrollButtons', scrollButtonsHideMobile && 'scrollButtonsHideMobile'],\n    scrollableX: [scrollableX && 'scrollableX'],\n    hideScrollbar: [hideScrollbar && 'hideScrollbar']\n  };\n  return composeClasses(slots, getTabsUtilityClass, classes);\n};\nconst TabsRoot = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${tabsClasses.scrollButtons}`]: styles.scrollButtons\n    }, {\n      [`& .${tabsClasses.scrollButtons}`]: ownerState.scrollButtonsHideMobile && styles.scrollButtonsHideMobile\n    }, styles.root, ownerState.vertical && styles.vertical];\n  }\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  overflow: 'hidden',\n  minHeight: 48,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  display: 'flex'\n}, ownerState.vertical && {\n  flexDirection: 'column'\n}, ownerState.scrollButtonsHideMobile && {\n  [`& .${tabsClasses.scrollButtons}`]: {\n    [theme.breakpoints.down('sm')]: {\n      display: 'none'\n    }\n  }\n}));\nconst TabsScroller = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Scroller',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.scroller, ownerState.fixed && styles.fixed, ownerState.hideScrollbar && styles.hideScrollbar, ownerState.scrollableX && styles.scrollableX, ownerState.scrollableY && styles.scrollableY];\n  }\n})(({\n  ownerState\n}) => _extends({\n  position: 'relative',\n  display: 'inline-block',\n  flex: '1 1 auto',\n  whiteSpace: 'nowrap'\n}, ownerState.fixed && {\n  overflowX: 'hidden',\n  width: '100%'\n}, ownerState.hideScrollbar && {\n  // Hide dimensionless scrollbar on macOS\n  scrollbarWidth: 'none',\n  // Firefox\n  '&::-webkit-scrollbar': {\n    display: 'none' // Safari + Chrome\n  }\n}, ownerState.scrollableX && {\n  overflowX: 'auto',\n  overflowY: 'hidden'\n}, ownerState.scrollableY && {\n  overflowY: 'auto',\n  overflowX: 'hidden'\n}));\nconst FlexContainer = styled('div', {\n  name: 'MuiTabs',\n  slot: 'FlexContainer',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.flexContainer, ownerState.vertical && styles.flexContainerVertical, ownerState.centered && styles.centered];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex'\n}, ownerState.vertical && {\n  flexDirection: 'column'\n}, ownerState.centered && {\n  justifyContent: 'center'\n}));\nconst TabsIndicator = styled('span', {\n  name: 'MuiTabs',\n  slot: 'Indicator',\n  overridesResolver: (props, styles) => styles.indicator\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  position: 'absolute',\n  height: 2,\n  bottom: 0,\n  width: '100%',\n  transition: theme.transitions.create()\n}, ownerState.indicatorColor === 'primary' && {\n  backgroundColor: (theme.vars || theme).palette.primary.main\n}, ownerState.indicatorColor === 'secondary' && {\n  backgroundColor: (theme.vars || theme).palette.secondary.main\n}, ownerState.vertical && {\n  height: '100%',\n  width: 2,\n  right: 0\n}));\nconst TabsScrollbarSize = styled(ScrollbarSize)({\n  overflowX: 'auto',\n  overflowY: 'hidden',\n  // Hide dimensionless scrollbar on macOS\n  scrollbarWidth: 'none',\n  // Firefox\n  '&::-webkit-scrollbar': {\n    display: 'none' // Safari + Chrome\n  }\n});\nconst defaultIndicatorStyle = {};\nlet warnedOnceTabPresent = false;\nconst Tabs = /*#__PURE__*/React.forwardRef(function Tabs(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTabs'\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const {\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledBy,\n      action,\n      centered = false,\n      children: childrenProp,\n      className,\n      component = 'div',\n      allowScrollButtonsMobile = false,\n      indicatorColor = 'primary',\n      onChange,\n      orientation = 'horizontal',\n      ScrollButtonComponent = TabScrollButton,\n      scrollButtons = 'auto',\n      selectionFollowsFocus,\n      slots = {},\n      slotProps = {},\n      TabIndicatorProps = {},\n      TabScrollButtonProps = {},\n      textColor = 'primary',\n      value,\n      variant = 'standard',\n      visibleScrollbar = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const scrollable = variant === 'scrollable';\n  const vertical = orientation === 'vertical';\n  const scrollStart = vertical ? 'scrollTop' : 'scrollLeft';\n  const start = vertical ? 'top' : 'left';\n  const end = vertical ? 'bottom' : 'right';\n  const clientSize = vertical ? 'clientHeight' : 'clientWidth';\n  const size = vertical ? 'height' : 'width';\n  const ownerState = _extends({}, props, {\n    component,\n    allowScrollButtonsMobile,\n    indicatorColor,\n    orientation,\n    vertical,\n    scrollButtons,\n    textColor,\n    variant,\n    visibleScrollbar,\n    fixed: !scrollable,\n    hideScrollbar: scrollable && !visibleScrollbar,\n    scrollableX: scrollable && !vertical,\n    scrollableY: scrollable && vertical,\n    centered: centered && !scrollable,\n    scrollButtonsHideMobile: !allowScrollButtonsMobile\n  });\n  const classes = useUtilityClasses(ownerState);\n  const startScrollButtonIconProps = useSlotProps({\n    elementType: slots.StartScrollButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    ownerState\n  });\n  const endScrollButtonIconProps = useSlotProps({\n    elementType: slots.EndScrollButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    ownerState\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (centered && scrollable) {\n      console.error('MUI: You can not use the `centered={true}` and `variant=\"scrollable\"` properties ' + 'at the same time on a `Tabs` component.');\n    }\n  }\n  const [mounted, setMounted] = React.useState(false);\n  const [indicatorStyle, setIndicatorStyle] = React.useState(defaultIndicatorStyle);\n  const [displayStartScroll, setDisplayStartScroll] = React.useState(false);\n  const [displayEndScroll, setDisplayEndScroll] = React.useState(false);\n  const [updateScrollObserver, setUpdateScrollObserver] = React.useState(false);\n  const [scrollerStyle, setScrollerStyle] = React.useState({\n    overflow: 'hidden',\n    scrollbarWidth: 0\n  });\n  const valueToIndex = new Map();\n  const tabsRef = React.useRef(null);\n  const tabListRef = React.useRef(null);\n  const getTabsMeta = () => {\n    const tabsNode = tabsRef.current;\n    let tabsMeta;\n    if (tabsNode) {\n      const rect = tabsNode.getBoundingClientRect();\n      // create a new object with ClientRect class props + scrollLeft\n      tabsMeta = {\n        clientWidth: tabsNode.clientWidth,\n        scrollLeft: tabsNode.scrollLeft,\n        scrollTop: tabsNode.scrollTop,\n        scrollLeftNormalized: getNormalizedScrollLeft(tabsNode, isRtl ? 'rtl' : 'ltr'),\n        scrollWidth: tabsNode.scrollWidth,\n        top: rect.top,\n        bottom: rect.bottom,\n        left: rect.left,\n        right: rect.right\n      };\n    }\n    let tabMeta;\n    if (tabsNode && value !== false) {\n      const children = tabListRef.current.children;\n      if (children.length > 0) {\n        const tab = children[valueToIndex.get(value)];\n        if (process.env.NODE_ENV !== 'production') {\n          if (!tab) {\n            console.error([`MUI: The \\`value\\` provided to the Tabs component is invalid.`, `None of the Tabs' children match with \"${value}\".`, valueToIndex.keys ? `You can provide one of the following values: ${Array.from(valueToIndex.keys()).join(', ')}.` : null].join('\\n'));\n          }\n        }\n        tabMeta = tab ? tab.getBoundingClientRect() : null;\n        if (process.env.NODE_ENV !== 'production') {\n          if (process.env.NODE_ENV !== 'test' && !warnedOnceTabPresent && tabMeta && tabMeta.width === 0 && tabMeta.height === 0 &&\n          // if the whole Tabs component is hidden, don't warn\n          tabsMeta.clientWidth !== 0) {\n            tabsMeta = null;\n            console.error(['MUI: The `value` provided to the Tabs component is invalid.', `The Tab with this \\`value\\` (\"${value}\") is not part of the document layout.`, \"Make sure the tab item is present in the document or that it's not `display: none`.\"].join('\\n'));\n            warnedOnceTabPresent = true;\n          }\n        }\n      }\n    }\n    return {\n      tabsMeta,\n      tabMeta\n    };\n  };\n  const updateIndicatorState = useEventCallback(() => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    let startValue = 0;\n    let startIndicator;\n    if (vertical) {\n      startIndicator = 'top';\n      if (tabMeta && tabsMeta) {\n        startValue = tabMeta.top - tabsMeta.top + tabsMeta.scrollTop;\n      }\n    } else {\n      startIndicator = isRtl ? 'right' : 'left';\n      if (tabMeta && tabsMeta) {\n        const correction = isRtl ? tabsMeta.scrollLeftNormalized + tabsMeta.clientWidth - tabsMeta.scrollWidth : tabsMeta.scrollLeft;\n        startValue = (isRtl ? -1 : 1) * (tabMeta[startIndicator] - tabsMeta[startIndicator] + correction);\n      }\n    }\n    const newIndicatorStyle = {\n      [startIndicator]: startValue,\n      // May be wrong until the font is loaded.\n      [size]: tabMeta ? tabMeta[size] : 0\n    };\n\n    // IE11 support, replace with Number.isNaN\n    // eslint-disable-next-line no-restricted-globals\n    if (isNaN(indicatorStyle[startIndicator]) || isNaN(indicatorStyle[size])) {\n      setIndicatorStyle(newIndicatorStyle);\n    } else {\n      const dStart = Math.abs(indicatorStyle[startIndicator] - newIndicatorStyle[startIndicator]);\n      const dSize = Math.abs(indicatorStyle[size] - newIndicatorStyle[size]);\n      if (dStart >= 1 || dSize >= 1) {\n        setIndicatorStyle(newIndicatorStyle);\n      }\n    }\n  });\n  const scroll = (scrollValue, {\n    animation = true\n  } = {}) => {\n    if (animation) {\n      animate(scrollStart, tabsRef.current, scrollValue, {\n        duration: theme.transitions.duration.standard\n      });\n    } else {\n      tabsRef.current[scrollStart] = scrollValue;\n    }\n  };\n  const moveTabsScroll = delta => {\n    let scrollValue = tabsRef.current[scrollStart];\n    if (vertical) {\n      scrollValue += delta;\n    } else {\n      scrollValue += delta * (isRtl ? -1 : 1);\n      // Fix for Edge\n      scrollValue *= isRtl && detectScrollType() === 'reverse' ? -1 : 1;\n    }\n    scroll(scrollValue);\n  };\n  const getScrollSize = () => {\n    const containerSize = tabsRef.current[clientSize];\n    let totalSize = 0;\n    const children = Array.from(tabListRef.current.children);\n    for (let i = 0; i < children.length; i += 1) {\n      const tab = children[i];\n      if (totalSize + tab[clientSize] > containerSize) {\n        // If the first item is longer than the container size, then only scroll\n        // by the container size.\n        if (i === 0) {\n          totalSize = containerSize;\n        }\n        break;\n      }\n      totalSize += tab[clientSize];\n    }\n    return totalSize;\n  };\n  const handleStartScrollClick = () => {\n    moveTabsScroll(-1 * getScrollSize());\n  };\n  const handleEndScrollClick = () => {\n    moveTabsScroll(getScrollSize());\n  };\n\n  // TODO Remove <ScrollbarSize /> as browser support for hiding the scrollbar\n  // with CSS improves.\n  const handleScrollbarSizeChange = React.useCallback(scrollbarWidth => {\n    setScrollerStyle({\n      overflow: null,\n      scrollbarWidth\n    });\n  }, []);\n  const getConditionalElements = () => {\n    const conditionalElements = {};\n    conditionalElements.scrollbarSizeListener = scrollable ? /*#__PURE__*/_jsx(TabsScrollbarSize, {\n      onChange: handleScrollbarSizeChange,\n      className: clsx(classes.scrollableX, classes.hideScrollbar)\n    }) : null;\n    const scrollButtonsActive = displayStartScroll || displayEndScroll;\n    const showScrollButtons = scrollable && (scrollButtons === 'auto' && scrollButtonsActive || scrollButtons === true);\n    conditionalElements.scrollButtonStart = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonComponent, _extends({\n      slots: {\n        StartScrollButtonIcon: slots.StartScrollButtonIcon\n      },\n      slotProps: {\n        startScrollButtonIcon: startScrollButtonIconProps\n      },\n      orientation: orientation,\n      direction: isRtl ? 'right' : 'left',\n      onClick: handleStartScrollClick,\n      disabled: !displayStartScroll\n    }, TabScrollButtonProps, {\n      className: clsx(classes.scrollButtons, TabScrollButtonProps.className)\n    })) : null;\n    conditionalElements.scrollButtonEnd = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonComponent, _extends({\n      slots: {\n        EndScrollButtonIcon: slots.EndScrollButtonIcon\n      },\n      slotProps: {\n        endScrollButtonIcon: endScrollButtonIconProps\n      },\n      orientation: orientation,\n      direction: isRtl ? 'left' : 'right',\n      onClick: handleEndScrollClick,\n      disabled: !displayEndScroll\n    }, TabScrollButtonProps, {\n      className: clsx(classes.scrollButtons, TabScrollButtonProps.className)\n    })) : null;\n    return conditionalElements;\n  };\n  const scrollSelectedIntoView = useEventCallback(animation => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    if (!tabMeta || !tabsMeta) {\n      return;\n    }\n    if (tabMeta[start] < tabsMeta[start]) {\n      // left side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[start] - tabsMeta[start]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    } else if (tabMeta[end] > tabsMeta[end]) {\n      // right side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[end] - tabsMeta[end]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    }\n  });\n  const updateScrollButtonState = useEventCallback(() => {\n    if (scrollable && scrollButtons !== false) {\n      setUpdateScrollObserver(!updateScrollObserver);\n    }\n  });\n  React.useEffect(() => {\n    const handleResize = debounce(() => {\n      // If the Tabs component is replaced by Suspense with a fallback, the last\n      // ResizeObserver's handler that runs because of the change in the layout is trying to\n      // access a dom node that is no longer there (as the fallback component is being shown instead).\n      // See https://github.com/mui/material-ui/issues/33276\n      // TODO: Add tests that will ensure the component is not failing when\n      // replaced by Suspense with a fallback, once React is updated to version 18\n      if (tabsRef.current) {\n        updateIndicatorState();\n      }\n    });\n    let resizeObserver;\n\n    /**\n     * @type {MutationCallback}\n     */\n    const handleMutation = records => {\n      records.forEach(record => {\n        record.removedNodes.forEach(item => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.unobserve(item);\n        });\n        record.addedNodes.forEach(item => {\n          var _resizeObserver2;\n          (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.observe(item);\n        });\n      });\n      handleResize();\n      updateScrollButtonState();\n    };\n    const win = ownerWindow(tabsRef.current);\n    win.addEventListener('resize', handleResize);\n    let mutationObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(handleResize);\n      Array.from(tabListRef.current.children).forEach(child => {\n        resizeObserver.observe(child);\n      });\n    }\n    if (typeof MutationObserver !== 'undefined') {\n      mutationObserver = new MutationObserver(handleMutation);\n      mutationObserver.observe(tabListRef.current, {\n        childList: true\n      });\n    }\n    return () => {\n      var _mutationObserver, _resizeObserver3;\n      handleResize.clear();\n      win.removeEventListener('resize', handleResize);\n      (_mutationObserver = mutationObserver) == null || _mutationObserver.disconnect();\n      (_resizeObserver3 = resizeObserver) == null || _resizeObserver3.disconnect();\n    };\n  }, [updateIndicatorState, updateScrollButtonState]);\n\n  /**\n   * Toggle visibility of start and end scroll buttons\n   * Using IntersectionObserver on first and last Tabs.\n   */\n  React.useEffect(() => {\n    const tabListChildren = Array.from(tabListRef.current.children);\n    const length = tabListChildren.length;\n    if (typeof IntersectionObserver !== 'undefined' && length > 0 && scrollable && scrollButtons !== false) {\n      const firstTab = tabListChildren[0];\n      const lastTab = tabListChildren[length - 1];\n      const observerOptions = {\n        root: tabsRef.current,\n        threshold: 0.99\n      };\n      const handleScrollButtonStart = entries => {\n        setDisplayStartScroll(!entries[0].isIntersecting);\n      };\n      const firstObserver = new IntersectionObserver(handleScrollButtonStart, observerOptions);\n      firstObserver.observe(firstTab);\n      const handleScrollButtonEnd = entries => {\n        setDisplayEndScroll(!entries[0].isIntersecting);\n      };\n      const lastObserver = new IntersectionObserver(handleScrollButtonEnd, observerOptions);\n      lastObserver.observe(lastTab);\n      return () => {\n        firstObserver.disconnect();\n        lastObserver.disconnect();\n      };\n    }\n    return undefined;\n  }, [scrollable, scrollButtons, updateScrollObserver, childrenProp == null ? void 0 : childrenProp.length]);\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    updateIndicatorState();\n  });\n  React.useEffect(() => {\n    // Don't animate on the first render.\n    scrollSelectedIntoView(defaultIndicatorStyle !== indicatorStyle);\n  }, [scrollSelectedIntoView, indicatorStyle]);\n  React.useImperativeHandle(action, () => ({\n    updateIndicator: updateIndicatorState,\n    updateScrollButtons: updateScrollButtonState\n  }), [updateIndicatorState, updateScrollButtonState]);\n  const indicator = /*#__PURE__*/_jsx(TabsIndicator, _extends({}, TabIndicatorProps, {\n    className: clsx(classes.indicator, TabIndicatorProps.className),\n    ownerState: ownerState,\n    style: _extends({}, indicatorStyle, TabIndicatorProps.style)\n  }));\n  let childIndex = 0;\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    const childValue = child.props.value === undefined ? childIndex : child.props.value;\n    valueToIndex.set(childValue, childIndex);\n    const selected = childValue === value;\n    childIndex += 1;\n    return /*#__PURE__*/React.cloneElement(child, _extends({\n      fullWidth: variant === 'fullWidth',\n      indicator: selected && !mounted && indicator,\n      selected,\n      selectionFollowsFocus,\n      onChange,\n      textColor,\n      value: childValue\n    }, childIndex === 1 && value === false && !child.props.tabIndex ? {\n      tabIndex: 0\n    } : {}));\n  });\n  const handleKeyDown = event => {\n    const list = tabListRef.current;\n    const currentFocus = ownerDocument(list).activeElement;\n    // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n    const role = currentFocus.getAttribute('role');\n    if (role !== 'tab') {\n      return;\n    }\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n      default:\n        break;\n    }\n  };\n  const conditionalElements = getConditionalElements();\n  return /*#__PURE__*/_jsxs(TabsRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    as: component\n  }, other, {\n    children: [conditionalElements.scrollButtonStart, conditionalElements.scrollbarSizeListener, /*#__PURE__*/_jsxs(TabsScroller, {\n      className: classes.scroller,\n      ownerState: ownerState,\n      style: {\n        overflow: scrollerStyle.overflow,\n        [vertical ? `margin${isRtl ? 'Left' : 'Right'}` : 'marginBottom']: visibleScrollbar ? undefined : -scrollerStyle.scrollbarWidth\n      },\n      ref: tabsRef,\n      children: [/*#__PURE__*/_jsx(FlexContainer, {\n        \"aria-label\": ariaLabel,\n        \"aria-labelledby\": ariaLabelledBy,\n        \"aria-orientation\": orientation === 'vertical' ? 'vertical' : null,\n        className: classes.flexContainer,\n        ownerState: ownerState,\n        onKeyDown: handleKeyDown,\n        ref: tabListRef,\n        role: \"tablist\",\n        children: children\n      }), mounted && indicator]\n    }), conditionalElements.scrollButtonEnd]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Tabs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Callback fired when the component mounts.\n   * This is useful when you want to trigger an action programmatically.\n   * It supports two actions: `updateIndicator()` and `updateScrollButtons()`\n   *\n   * @param {object} actions This object contains all possible actions\n   * that can be triggered programmatically.\n   */\n  action: refType,\n  /**\n   * If `true`, the scroll buttons aren't forced hidden on mobile.\n   * By default the scroll buttons are hidden on mobile and takes precedence over `scrollButtons`.\n   * @default false\n   */\n  allowScrollButtonsMobile: PropTypes.bool,\n  /**\n   * The label for the Tabs as a string.\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * An id or list of ids separated by a space that label the Tabs.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * If `true`, the tabs are centered.\n   * This prop is intended for large views.\n   * @default false\n   */\n  centered: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Determines the color of the indicator.\n   * @default 'primary'\n   */\n  indicatorColor: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary']), PropTypes.string]),\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {any} value We default to the index of the child (number)\n   */\n  onChange: PropTypes.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The component used to render the scroll buttons.\n   * @default TabScrollButton\n   */\n  ScrollButtonComponent: PropTypes.elementType,\n  /**\n   * Determine behavior of scroll buttons when tabs are set to scroll:\n   *\n   * - `auto` will only present them when not all the items are visible.\n   * - `true` will always present them.\n   * - `false` will never present them.\n   *\n   * By default the scroll buttons are hidden on mobile.\n   * This behavior can be disabled with `allowScrollButtonsMobile`.\n   * @default 'auto'\n   */\n  scrollButtons: PropTypes /* @typescript-to-proptypes-ignore */.oneOf(['auto', false, true]),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    EndScrollButtonIcon: PropTypes.elementType,\n    StartScrollButtonIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Props applied to the tab indicator element.\n   * @default  {}\n   */\n  TabIndicatorProps: PropTypes.object,\n  /**\n   * Props applied to the [`TabScrollButton`](/material-ui/api/tab-scroll-button/) element.\n   * @default {}\n   */\n  TabScrollButtonProps: PropTypes.object,\n  /**\n   * Determines the color of the `Tab`.\n   * @default 'primary'\n   */\n  textColor: PropTypes.oneOf(['inherit', 'primary', 'secondary']),\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: PropTypes.any,\n  /**\n   * Determines additional display behavior of the tabs:\n   *\n   *  - `scrollable` will invoke scrolling properties and allow for horizontally\n   *  scrolling (or swiping) of the tab bar.\n   *  - `fullWidth` will make the tabs grow to use all the available space,\n   *  which should be used for small views, like on mobile.\n   *  - `standard` will render the default state.\n   * @default 'standard'\n   */\n  variant: PropTypes.oneOf(['fullWidth', 'scrollable', 'standard']),\n  /**\n   * If `true`, the scrollbar is visible. It can be useful when displaying\n   * a long vertical list of tabs.\n   * @default false\n   */\n  visibleScrollbar: PropTypes.bool\n} : void 0;\nexport default Tabs;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "isFragment", "PropTypes", "clsx", "refType", "composeClasses", "useRtl", "useSlotProps", "styled", "useDefaultProps", "useTheme", "debounce", "getNormalizedScrollLeft", "detectScrollType", "animate", "ScrollbarSize", "TabScrollButton", "useEventCallback", "tabsClasses", "getTabsUtilityClass", "ownerDocument", "ownerWindow", "jsx", "_jsx", "jsxs", "_jsxs", "nextItem", "list", "item", "<PERSON><PERSON><PERSON><PERSON>", "nextElement<PERSON><PERSON>ling", "previousItem", "<PERSON><PERSON><PERSON><PERSON>", "previousElementSibling", "moveFocus", "currentFocus", "traversalFunction", "wrappedOnce", "nextFocus", "nextFocusDisabled", "disabled", "getAttribute", "hasAttribute", "focus", "useUtilityClasses", "ownerState", "vertical", "fixed", "hideScrollbar", "scrollableX", "scrollableY", "centered", "scrollButtonsHideMobile", "classes", "slots", "root", "scroller", "flexContainer", "indicator", "scrollButtons", "TabsRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "overflow", "minHeight", "WebkitOverflowScrolling", "display", "flexDirection", "breakpoints", "down", "TabsScroller", "position", "flex", "whiteSpace", "overflowX", "width", "scrollbarWidth", "overflowY", "FlexContainer", "flexContainerVertical", "justifyContent", "TabsIndicator", "height", "bottom", "transition", "transitions", "create", "indicatorColor", "backgroundColor", "vars", "palette", "primary", "main", "secondary", "right", "TabsScrollbarSize", "defaultIndicatorStyle", "warnedOnceTabPresent", "Tabs", "forwardRef", "inProps", "ref", "isRtl", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "action", "children", "childrenProp", "className", "component", "allowScrollButtonsMobile", "onChange", "orientation", "ScrollButtonComponent", "selectionFollowsFocus", "slotProps", "TabIndicatorProps", "TabScrollButtonProps", "textColor", "value", "variant", "visibleScrollbar", "other", "scrollable", "scrollStart", "start", "end", "clientSize", "size", "startScrollButtonIconProps", "elementType", "StartScrollButtonIcon", "externalSlotProps", "startScrollButtonIcon", "endScrollButtonIconProps", "EndScrollButtonIcon", "endScrollButtonIcon", "process", "env", "NODE_ENV", "console", "error", "mounted", "setMounted", "useState", "indicatorStyle", "setIndicatorStyle", "displayStartScroll", "setDisplayStartScroll", "displayEndScroll", "setDisplayEndScroll", "updateScrollObserver", "setUpdateScrollObserver", "scrollerStyle", "setScrollerStyle", "valueToIndex", "Map", "tabsRef", "useRef", "tabListRef", "getTabsMeta", "tabsNode", "current", "tabsMeta", "rect", "getBoundingClientRect", "clientWidth", "scrollLeft", "scrollTop", "scrollLeftNormalized", "scrollWidth", "top", "left", "tabMeta", "length", "tab", "get", "keys", "Array", "from", "join", "updateIndicatorState", "startValue", "startIndicator", "correction", "newIndicatorStyle", "isNaN", "dStart", "Math", "abs", "dSize", "scroll", "scrollValue", "animation", "duration", "standard", "moveTabsScroll", "delta", "getScrollSize", "containerSize", "totalSize", "i", "handleStartScrollClick", "handleEndScrollClick", "handleScrollbarSizeChange", "useCallback", "getConditionalElements", "conditionalElements", "scrollbarSizeListener", "scrollButtonsActive", "showScrollButtons", "scrollButtonStart", "direction", "onClick", "scrollButtonEnd", "scrollSelectedIntoView", "nextScrollStart", "updateScrollButtonState", "useEffect", "handleResize", "resizeObserver", "handleMutation", "records", "for<PERSON>ach", "record", "removedNodes", "_resizeObserver", "unobserve", "addedNodes", "_resizeObserver2", "observe", "win", "addEventListener", "mutationObserver", "ResizeObserver", "child", "MutationObserver", "childList", "_mutationObserver", "_resizeObserver3", "clear", "removeEventListener", "disconnect", "tabList<PERSON><PERSON><PERSON>n", "IntersectionObserver", "firstTab", "lastTab", "observerOptions", "threshold", "handleScrollButtonStart", "entries", "isIntersecting", "firstObserver", "handleScrollButtonEnd", "lastObserver", "undefined", "useImperativeHandle", "updateIndicator", "updateScrollButtons", "style", "childIndex", "Children", "map", "isValidElement", "childValue", "set", "selected", "cloneElement", "fullWidth", "tabIndex", "handleKeyDown", "event", "activeElement", "role", "previousItemKey", "nextItemKey", "key", "preventDefault", "as", "onKeyDown", "propTypes", "bool", "string", "node", "object", "oneOfType", "oneOf", "func", "shape", "sx", "arrayOf", "any"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/Tabs/Tabs.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"aria-label\", \"aria-labelledby\", \"action\", \"centered\", \"children\", \"className\", \"component\", \"allowScrollButtonsMobile\", \"indicatorColor\", \"onChange\", \"orientation\", \"ScrollButtonComponent\", \"scrollButtons\", \"selectionFollowsFocus\", \"slots\", \"slotProps\", \"TabIndicatorProps\", \"TabScrollButtonProps\", \"textColor\", \"value\", \"variant\", \"visibleScrollbar\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport debounce from '../utils/debounce';\nimport { getNormalizedScrollLeft, detectScrollType } from '../utils/scrollLeft';\nimport animate from '../internal/animate';\nimport ScrollbarSize from './ScrollbarSize';\nimport TabScrollButton from '../TabScrollButton';\nimport useEventCallback from '../utils/useEventCallback';\nimport tabsClasses, { getTabsUtilityClass } from './tabsClasses';\nimport ownerDocument from '../utils/ownerDocument';\nimport ownerWindow from '../utils/ownerWindow';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst nextItem = (list, item) => {\n  if (list === item) {\n    return list.firstChild;\n  }\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n  return list.firstChild;\n};\nconst previousItem = (list, item) => {\n  if (list === item) {\n    return list.lastChild;\n  }\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n  return list.lastChild;\n};\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n  while (nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n      wrappedOnce = true;\n    }\n\n    // Same logic as useAutocomplete.js\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    vertical,\n    fixed,\n    hideScrollbar,\n    scrollableX,\n    scrollableY,\n    centered,\n    scrollButtonsHideMobile,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', vertical && 'vertical'],\n    scroller: ['scroller', fixed && 'fixed', hideScrollbar && 'hideScrollbar', scrollableX && 'scrollableX', scrollableY && 'scrollableY'],\n    flexContainer: ['flexContainer', vertical && 'flexContainerVertical', centered && 'centered'],\n    indicator: ['indicator'],\n    scrollButtons: ['scrollButtons', scrollButtonsHideMobile && 'scrollButtonsHideMobile'],\n    scrollableX: [scrollableX && 'scrollableX'],\n    hideScrollbar: [hideScrollbar && 'hideScrollbar']\n  };\n  return composeClasses(slots, getTabsUtilityClass, classes);\n};\nconst TabsRoot = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${tabsClasses.scrollButtons}`]: styles.scrollButtons\n    }, {\n      [`& .${tabsClasses.scrollButtons}`]: ownerState.scrollButtonsHideMobile && styles.scrollButtonsHideMobile\n    }, styles.root, ownerState.vertical && styles.vertical];\n  }\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  overflow: 'hidden',\n  minHeight: 48,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  display: 'flex'\n}, ownerState.vertical && {\n  flexDirection: 'column'\n}, ownerState.scrollButtonsHideMobile && {\n  [`& .${tabsClasses.scrollButtons}`]: {\n    [theme.breakpoints.down('sm')]: {\n      display: 'none'\n    }\n  }\n}));\nconst TabsScroller = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Scroller',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.scroller, ownerState.fixed && styles.fixed, ownerState.hideScrollbar && styles.hideScrollbar, ownerState.scrollableX && styles.scrollableX, ownerState.scrollableY && styles.scrollableY];\n  }\n})(({\n  ownerState\n}) => _extends({\n  position: 'relative',\n  display: 'inline-block',\n  flex: '1 1 auto',\n  whiteSpace: 'nowrap'\n}, ownerState.fixed && {\n  overflowX: 'hidden',\n  width: '100%'\n}, ownerState.hideScrollbar && {\n  // Hide dimensionless scrollbar on macOS\n  scrollbarWidth: 'none',\n  // Firefox\n  '&::-webkit-scrollbar': {\n    display: 'none' // Safari + Chrome\n  }\n}, ownerState.scrollableX && {\n  overflowX: 'auto',\n  overflowY: 'hidden'\n}, ownerState.scrollableY && {\n  overflowY: 'auto',\n  overflowX: 'hidden'\n}));\nconst FlexContainer = styled('div', {\n  name: 'MuiTabs',\n  slot: 'FlexContainer',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.flexContainer, ownerState.vertical && styles.flexContainerVertical, ownerState.centered && styles.centered];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex'\n}, ownerState.vertical && {\n  flexDirection: 'column'\n}, ownerState.centered && {\n  justifyContent: 'center'\n}));\nconst TabsIndicator = styled('span', {\n  name: 'MuiTabs',\n  slot: 'Indicator',\n  overridesResolver: (props, styles) => styles.indicator\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  position: 'absolute',\n  height: 2,\n  bottom: 0,\n  width: '100%',\n  transition: theme.transitions.create()\n}, ownerState.indicatorColor === 'primary' && {\n  backgroundColor: (theme.vars || theme).palette.primary.main\n}, ownerState.indicatorColor === 'secondary' && {\n  backgroundColor: (theme.vars || theme).palette.secondary.main\n}, ownerState.vertical && {\n  height: '100%',\n  width: 2,\n  right: 0\n}));\nconst TabsScrollbarSize = styled(ScrollbarSize)({\n  overflowX: 'auto',\n  overflowY: 'hidden',\n  // Hide dimensionless scrollbar on macOS\n  scrollbarWidth: 'none',\n  // Firefox\n  '&::-webkit-scrollbar': {\n    display: 'none' // Safari + Chrome\n  }\n});\nconst defaultIndicatorStyle = {};\nlet warnedOnceTabPresent = false;\nconst Tabs = /*#__PURE__*/React.forwardRef(function Tabs(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTabs'\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const {\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledBy,\n      action,\n      centered = false,\n      children: childrenProp,\n      className,\n      component = 'div',\n      allowScrollButtonsMobile = false,\n      indicatorColor = 'primary',\n      onChange,\n      orientation = 'horizontal',\n      ScrollButtonComponent = TabScrollButton,\n      scrollButtons = 'auto',\n      selectionFollowsFocus,\n      slots = {},\n      slotProps = {},\n      TabIndicatorProps = {},\n      TabScrollButtonProps = {},\n      textColor = 'primary',\n      value,\n      variant = 'standard',\n      visibleScrollbar = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const scrollable = variant === 'scrollable';\n  const vertical = orientation === 'vertical';\n  const scrollStart = vertical ? 'scrollTop' : 'scrollLeft';\n  const start = vertical ? 'top' : 'left';\n  const end = vertical ? 'bottom' : 'right';\n  const clientSize = vertical ? 'clientHeight' : 'clientWidth';\n  const size = vertical ? 'height' : 'width';\n  const ownerState = _extends({}, props, {\n    component,\n    allowScrollButtonsMobile,\n    indicatorColor,\n    orientation,\n    vertical,\n    scrollButtons,\n    textColor,\n    variant,\n    visibleScrollbar,\n    fixed: !scrollable,\n    hideScrollbar: scrollable && !visibleScrollbar,\n    scrollableX: scrollable && !vertical,\n    scrollableY: scrollable && vertical,\n    centered: centered && !scrollable,\n    scrollButtonsHideMobile: !allowScrollButtonsMobile\n  });\n  const classes = useUtilityClasses(ownerState);\n  const startScrollButtonIconProps = useSlotProps({\n    elementType: slots.StartScrollButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    ownerState\n  });\n  const endScrollButtonIconProps = useSlotProps({\n    elementType: slots.EndScrollButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    ownerState\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (centered && scrollable) {\n      console.error('MUI: You can not use the `centered={true}` and `variant=\"scrollable\"` properties ' + 'at the same time on a `Tabs` component.');\n    }\n  }\n  const [mounted, setMounted] = React.useState(false);\n  const [indicatorStyle, setIndicatorStyle] = React.useState(defaultIndicatorStyle);\n  const [displayStartScroll, setDisplayStartScroll] = React.useState(false);\n  const [displayEndScroll, setDisplayEndScroll] = React.useState(false);\n  const [updateScrollObserver, setUpdateScrollObserver] = React.useState(false);\n  const [scrollerStyle, setScrollerStyle] = React.useState({\n    overflow: 'hidden',\n    scrollbarWidth: 0\n  });\n  const valueToIndex = new Map();\n  const tabsRef = React.useRef(null);\n  const tabListRef = React.useRef(null);\n  const getTabsMeta = () => {\n    const tabsNode = tabsRef.current;\n    let tabsMeta;\n    if (tabsNode) {\n      const rect = tabsNode.getBoundingClientRect();\n      // create a new object with ClientRect class props + scrollLeft\n      tabsMeta = {\n        clientWidth: tabsNode.clientWidth,\n        scrollLeft: tabsNode.scrollLeft,\n        scrollTop: tabsNode.scrollTop,\n        scrollLeftNormalized: getNormalizedScrollLeft(tabsNode, isRtl ? 'rtl' : 'ltr'),\n        scrollWidth: tabsNode.scrollWidth,\n        top: rect.top,\n        bottom: rect.bottom,\n        left: rect.left,\n        right: rect.right\n      };\n    }\n    let tabMeta;\n    if (tabsNode && value !== false) {\n      const children = tabListRef.current.children;\n      if (children.length > 0) {\n        const tab = children[valueToIndex.get(value)];\n        if (process.env.NODE_ENV !== 'production') {\n          if (!tab) {\n            console.error([`MUI: The \\`value\\` provided to the Tabs component is invalid.`, `None of the Tabs' children match with \"${value}\".`, valueToIndex.keys ? `You can provide one of the following values: ${Array.from(valueToIndex.keys()).join(', ')}.` : null].join('\\n'));\n          }\n        }\n        tabMeta = tab ? tab.getBoundingClientRect() : null;\n        if (process.env.NODE_ENV !== 'production') {\n          if (process.env.NODE_ENV !== 'test' && !warnedOnceTabPresent && tabMeta && tabMeta.width === 0 && tabMeta.height === 0 &&\n          // if the whole Tabs component is hidden, don't warn\n          tabsMeta.clientWidth !== 0) {\n            tabsMeta = null;\n            console.error(['MUI: The `value` provided to the Tabs component is invalid.', `The Tab with this \\`value\\` (\"${value}\") is not part of the document layout.`, \"Make sure the tab item is present in the document or that it's not `display: none`.\"].join('\\n'));\n            warnedOnceTabPresent = true;\n          }\n        }\n      }\n    }\n    return {\n      tabsMeta,\n      tabMeta\n    };\n  };\n  const updateIndicatorState = useEventCallback(() => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    let startValue = 0;\n    let startIndicator;\n    if (vertical) {\n      startIndicator = 'top';\n      if (tabMeta && tabsMeta) {\n        startValue = tabMeta.top - tabsMeta.top + tabsMeta.scrollTop;\n      }\n    } else {\n      startIndicator = isRtl ? 'right' : 'left';\n      if (tabMeta && tabsMeta) {\n        const correction = isRtl ? tabsMeta.scrollLeftNormalized + tabsMeta.clientWidth - tabsMeta.scrollWidth : tabsMeta.scrollLeft;\n        startValue = (isRtl ? -1 : 1) * (tabMeta[startIndicator] - tabsMeta[startIndicator] + correction);\n      }\n    }\n    const newIndicatorStyle = {\n      [startIndicator]: startValue,\n      // May be wrong until the font is loaded.\n      [size]: tabMeta ? tabMeta[size] : 0\n    };\n\n    // IE11 support, replace with Number.isNaN\n    // eslint-disable-next-line no-restricted-globals\n    if (isNaN(indicatorStyle[startIndicator]) || isNaN(indicatorStyle[size])) {\n      setIndicatorStyle(newIndicatorStyle);\n    } else {\n      const dStart = Math.abs(indicatorStyle[startIndicator] - newIndicatorStyle[startIndicator]);\n      const dSize = Math.abs(indicatorStyle[size] - newIndicatorStyle[size]);\n      if (dStart >= 1 || dSize >= 1) {\n        setIndicatorStyle(newIndicatorStyle);\n      }\n    }\n  });\n  const scroll = (scrollValue, {\n    animation = true\n  } = {}) => {\n    if (animation) {\n      animate(scrollStart, tabsRef.current, scrollValue, {\n        duration: theme.transitions.duration.standard\n      });\n    } else {\n      tabsRef.current[scrollStart] = scrollValue;\n    }\n  };\n  const moveTabsScroll = delta => {\n    let scrollValue = tabsRef.current[scrollStart];\n    if (vertical) {\n      scrollValue += delta;\n    } else {\n      scrollValue += delta * (isRtl ? -1 : 1);\n      // Fix for Edge\n      scrollValue *= isRtl && detectScrollType() === 'reverse' ? -1 : 1;\n    }\n    scroll(scrollValue);\n  };\n  const getScrollSize = () => {\n    const containerSize = tabsRef.current[clientSize];\n    let totalSize = 0;\n    const children = Array.from(tabListRef.current.children);\n    for (let i = 0; i < children.length; i += 1) {\n      const tab = children[i];\n      if (totalSize + tab[clientSize] > containerSize) {\n        // If the first item is longer than the container size, then only scroll\n        // by the container size.\n        if (i === 0) {\n          totalSize = containerSize;\n        }\n        break;\n      }\n      totalSize += tab[clientSize];\n    }\n    return totalSize;\n  };\n  const handleStartScrollClick = () => {\n    moveTabsScroll(-1 * getScrollSize());\n  };\n  const handleEndScrollClick = () => {\n    moveTabsScroll(getScrollSize());\n  };\n\n  // TODO Remove <ScrollbarSize /> as browser support for hiding the scrollbar\n  // with CSS improves.\n  const handleScrollbarSizeChange = React.useCallback(scrollbarWidth => {\n    setScrollerStyle({\n      overflow: null,\n      scrollbarWidth\n    });\n  }, []);\n  const getConditionalElements = () => {\n    const conditionalElements = {};\n    conditionalElements.scrollbarSizeListener = scrollable ? /*#__PURE__*/_jsx(TabsScrollbarSize, {\n      onChange: handleScrollbarSizeChange,\n      className: clsx(classes.scrollableX, classes.hideScrollbar)\n    }) : null;\n    const scrollButtonsActive = displayStartScroll || displayEndScroll;\n    const showScrollButtons = scrollable && (scrollButtons === 'auto' && scrollButtonsActive || scrollButtons === true);\n    conditionalElements.scrollButtonStart = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonComponent, _extends({\n      slots: {\n        StartScrollButtonIcon: slots.StartScrollButtonIcon\n      },\n      slotProps: {\n        startScrollButtonIcon: startScrollButtonIconProps\n      },\n      orientation: orientation,\n      direction: isRtl ? 'right' : 'left',\n      onClick: handleStartScrollClick,\n      disabled: !displayStartScroll\n    }, TabScrollButtonProps, {\n      className: clsx(classes.scrollButtons, TabScrollButtonProps.className)\n    })) : null;\n    conditionalElements.scrollButtonEnd = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonComponent, _extends({\n      slots: {\n        EndScrollButtonIcon: slots.EndScrollButtonIcon\n      },\n      slotProps: {\n        endScrollButtonIcon: endScrollButtonIconProps\n      },\n      orientation: orientation,\n      direction: isRtl ? 'left' : 'right',\n      onClick: handleEndScrollClick,\n      disabled: !displayEndScroll\n    }, TabScrollButtonProps, {\n      className: clsx(classes.scrollButtons, TabScrollButtonProps.className)\n    })) : null;\n    return conditionalElements;\n  };\n  const scrollSelectedIntoView = useEventCallback(animation => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    if (!tabMeta || !tabsMeta) {\n      return;\n    }\n    if (tabMeta[start] < tabsMeta[start]) {\n      // left side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[start] - tabsMeta[start]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    } else if (tabMeta[end] > tabsMeta[end]) {\n      // right side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[end] - tabsMeta[end]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    }\n  });\n  const updateScrollButtonState = useEventCallback(() => {\n    if (scrollable && scrollButtons !== false) {\n      setUpdateScrollObserver(!updateScrollObserver);\n    }\n  });\n  React.useEffect(() => {\n    const handleResize = debounce(() => {\n      // If the Tabs component is replaced by Suspense with a fallback, the last\n      // ResizeObserver's handler that runs because of the change in the layout is trying to\n      // access a dom node that is no longer there (as the fallback component is being shown instead).\n      // See https://github.com/mui/material-ui/issues/33276\n      // TODO: Add tests that will ensure the component is not failing when\n      // replaced by Suspense with a fallback, once React is updated to version 18\n      if (tabsRef.current) {\n        updateIndicatorState();\n      }\n    });\n    let resizeObserver;\n\n    /**\n     * @type {MutationCallback}\n     */\n    const handleMutation = records => {\n      records.forEach(record => {\n        record.removedNodes.forEach(item => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.unobserve(item);\n        });\n        record.addedNodes.forEach(item => {\n          var _resizeObserver2;\n          (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.observe(item);\n        });\n      });\n      handleResize();\n      updateScrollButtonState();\n    };\n    const win = ownerWindow(tabsRef.current);\n    win.addEventListener('resize', handleResize);\n    let mutationObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(handleResize);\n      Array.from(tabListRef.current.children).forEach(child => {\n        resizeObserver.observe(child);\n      });\n    }\n    if (typeof MutationObserver !== 'undefined') {\n      mutationObserver = new MutationObserver(handleMutation);\n      mutationObserver.observe(tabListRef.current, {\n        childList: true\n      });\n    }\n    return () => {\n      var _mutationObserver, _resizeObserver3;\n      handleResize.clear();\n      win.removeEventListener('resize', handleResize);\n      (_mutationObserver = mutationObserver) == null || _mutationObserver.disconnect();\n      (_resizeObserver3 = resizeObserver) == null || _resizeObserver3.disconnect();\n    };\n  }, [updateIndicatorState, updateScrollButtonState]);\n\n  /**\n   * Toggle visibility of start and end scroll buttons\n   * Using IntersectionObserver on first and last Tabs.\n   */\n  React.useEffect(() => {\n    const tabListChildren = Array.from(tabListRef.current.children);\n    const length = tabListChildren.length;\n    if (typeof IntersectionObserver !== 'undefined' && length > 0 && scrollable && scrollButtons !== false) {\n      const firstTab = tabListChildren[0];\n      const lastTab = tabListChildren[length - 1];\n      const observerOptions = {\n        root: tabsRef.current,\n        threshold: 0.99\n      };\n      const handleScrollButtonStart = entries => {\n        setDisplayStartScroll(!entries[0].isIntersecting);\n      };\n      const firstObserver = new IntersectionObserver(handleScrollButtonStart, observerOptions);\n      firstObserver.observe(firstTab);\n      const handleScrollButtonEnd = entries => {\n        setDisplayEndScroll(!entries[0].isIntersecting);\n      };\n      const lastObserver = new IntersectionObserver(handleScrollButtonEnd, observerOptions);\n      lastObserver.observe(lastTab);\n      return () => {\n        firstObserver.disconnect();\n        lastObserver.disconnect();\n      };\n    }\n    return undefined;\n  }, [scrollable, scrollButtons, updateScrollObserver, childrenProp == null ? void 0 : childrenProp.length]);\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    updateIndicatorState();\n  });\n  React.useEffect(() => {\n    // Don't animate on the first render.\n    scrollSelectedIntoView(defaultIndicatorStyle !== indicatorStyle);\n  }, [scrollSelectedIntoView, indicatorStyle]);\n  React.useImperativeHandle(action, () => ({\n    updateIndicator: updateIndicatorState,\n    updateScrollButtons: updateScrollButtonState\n  }), [updateIndicatorState, updateScrollButtonState]);\n  const indicator = /*#__PURE__*/_jsx(TabsIndicator, _extends({}, TabIndicatorProps, {\n    className: clsx(classes.indicator, TabIndicatorProps.className),\n    ownerState: ownerState,\n    style: _extends({}, indicatorStyle, TabIndicatorProps.style)\n  }));\n  let childIndex = 0;\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    const childValue = child.props.value === undefined ? childIndex : child.props.value;\n    valueToIndex.set(childValue, childIndex);\n    const selected = childValue === value;\n    childIndex += 1;\n    return /*#__PURE__*/React.cloneElement(child, _extends({\n      fullWidth: variant === 'fullWidth',\n      indicator: selected && !mounted && indicator,\n      selected,\n      selectionFollowsFocus,\n      onChange,\n      textColor,\n      value: childValue\n    }, childIndex === 1 && value === false && !child.props.tabIndex ? {\n      tabIndex: 0\n    } : {}));\n  });\n  const handleKeyDown = event => {\n    const list = tabListRef.current;\n    const currentFocus = ownerDocument(list).activeElement;\n    // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n    const role = currentFocus.getAttribute('role');\n    if (role !== 'tab') {\n      return;\n    }\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n      default:\n        break;\n    }\n  };\n  const conditionalElements = getConditionalElements();\n  return /*#__PURE__*/_jsxs(TabsRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    as: component\n  }, other, {\n    children: [conditionalElements.scrollButtonStart, conditionalElements.scrollbarSizeListener, /*#__PURE__*/_jsxs(TabsScroller, {\n      className: classes.scroller,\n      ownerState: ownerState,\n      style: {\n        overflow: scrollerStyle.overflow,\n        [vertical ? `margin${isRtl ? 'Left' : 'Right'}` : 'marginBottom']: visibleScrollbar ? undefined : -scrollerStyle.scrollbarWidth\n      },\n      ref: tabsRef,\n      children: [/*#__PURE__*/_jsx(FlexContainer, {\n        \"aria-label\": ariaLabel,\n        \"aria-labelledby\": ariaLabelledBy,\n        \"aria-orientation\": orientation === 'vertical' ? 'vertical' : null,\n        className: classes.flexContainer,\n        ownerState: ownerState,\n        onKeyDown: handleKeyDown,\n        ref: tabListRef,\n        role: \"tablist\",\n        children: children\n      }), mounted && indicator]\n    }), conditionalElements.scrollButtonEnd]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Tabs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Callback fired when the component mounts.\n   * This is useful when you want to trigger an action programmatically.\n   * It supports two actions: `updateIndicator()` and `updateScrollButtons()`\n   *\n   * @param {object} actions This object contains all possible actions\n   * that can be triggered programmatically.\n   */\n  action: refType,\n  /**\n   * If `true`, the scroll buttons aren't forced hidden on mobile.\n   * By default the scroll buttons are hidden on mobile and takes precedence over `scrollButtons`.\n   * @default false\n   */\n  allowScrollButtonsMobile: PropTypes.bool,\n  /**\n   * The label for the Tabs as a string.\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * An id or list of ids separated by a space that label the Tabs.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * If `true`, the tabs are centered.\n   * This prop is intended for large views.\n   * @default false\n   */\n  centered: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Determines the color of the indicator.\n   * @default 'primary'\n   */\n  indicatorColor: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary']), PropTypes.string]),\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {any} value We default to the index of the child (number)\n   */\n  onChange: PropTypes.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The component used to render the scroll buttons.\n   * @default TabScrollButton\n   */\n  ScrollButtonComponent: PropTypes.elementType,\n  /**\n   * Determine behavior of scroll buttons when tabs are set to scroll:\n   *\n   * - `auto` will only present them when not all the items are visible.\n   * - `true` will always present them.\n   * - `false` will never present them.\n   *\n   * By default the scroll buttons are hidden on mobile.\n   * This behavior can be disabled with `allowScrollButtonsMobile`.\n   * @default 'auto'\n   */\n  scrollButtons: PropTypes /* @typescript-to-proptypes-ignore */.oneOf(['auto', false, true]),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    EndScrollButtonIcon: PropTypes.elementType,\n    StartScrollButtonIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Props applied to the tab indicator element.\n   * @default  {}\n   */\n  TabIndicatorProps: PropTypes.object,\n  /**\n   * Props applied to the [`TabScrollButton`](/material-ui/api/tab-scroll-button/) element.\n   * @default {}\n   */\n  TabScrollButtonProps: PropTypes.object,\n  /**\n   * Determines the color of the `Tab`.\n   * @default 'primary'\n   */\n  textColor: PropTypes.oneOf(['inherit', 'primary', 'secondary']),\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: PropTypes.any,\n  /**\n   * Determines additional display behavior of the tabs:\n   *\n   *  - `scrollable` will invoke scrolling properties and allow for horizontally\n   *  scrolling (or swiping) of the tab bar.\n   *  - `fullWidth` will make the tabs grow to use all the available space,\n   *  which should be used for small views, like on mobile.\n   *  - `standard` will render the default state.\n   * @default 'standard'\n   */\n  variant: PropTypes.oneOf(['fullWidth', 'scrollable', 'standard']),\n  /**\n   * If `true`, the scrollbar is visible. It can be useful when displaying\n   * a long vertical list of tabs.\n   * @default false\n   */\n  visibleScrollbar: PropTypes.bool\n} : void 0;\nexport default Tabs;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,YAAY,EAAE,iBAAiB,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,0BAA0B,EAAE,gBAAgB,EAAE,UAAU,EAAE,aAAa,EAAE,uBAAuB,EAAE,eAAe,EAAE,uBAAuB,EAAE,OAAO,EAAE,WAAW,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,CAAC;AACnX,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,uBAAuB,EAAEC,gBAAgB,QAAQ,qBAAqB;AAC/E,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,WAAW,IAAIC,mBAAmB,QAAQ,eAAe;AAChE,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAC/B,IAAID,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAOD,IAAI,CAACE,UAAU;EACxB;EACA,IAAID,IAAI,IAAIA,IAAI,CAACE,kBAAkB,EAAE;IACnC,OAAOF,IAAI,CAACE,kBAAkB;EAChC;EACA,OAAOH,IAAI,CAACE,UAAU;AACxB,CAAC;AACD,MAAME,YAAY,GAAGA,CAACJ,IAAI,EAAEC,IAAI,KAAK;EACnC,IAAID,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAOD,IAAI,CAACK,SAAS;EACvB;EACA,IAAIJ,IAAI,IAAIA,IAAI,CAACK,sBAAsB,EAAE;IACvC,OAAOL,IAAI,CAACK,sBAAsB;EACpC;EACA,OAAON,IAAI,CAACK,SAAS;AACvB,CAAC;AACD,MAAME,SAAS,GAAGA,CAACP,IAAI,EAAEQ,YAAY,EAAEC,iBAAiB,KAAK;EAC3D,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,SAAS,GAAGF,iBAAiB,CAACT,IAAI,EAAEQ,YAAY,CAAC;EACrD,OAAOG,SAAS,EAAE;IAChB;IACA,IAAIA,SAAS,KAAKX,IAAI,CAACE,UAAU,EAAE;MACjC,IAAIQ,WAAW,EAAE;QACf;MACF;MACAA,WAAW,GAAG,IAAI;IACpB;;IAEA;IACA,MAAME,iBAAiB,GAAGD,SAAS,CAACE,QAAQ,IAAIF,SAAS,CAACG,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;IAClG,IAAI,CAACH,SAAS,CAACI,YAAY,CAAC,UAAU,CAAC,IAAIH,iBAAiB,EAAE;MAC5D;MACAD,SAAS,GAAGF,iBAAiB,CAACT,IAAI,EAAEW,SAAS,CAAC;IAChD,CAAC,MAAM;MACLA,SAAS,CAACK,KAAK,CAAC,CAAC;MACjB;IACF;EACF;AACF,CAAC;AACD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC,KAAK;IACLC,aAAa;IACbC,WAAW;IACXC,WAAW;IACXC,QAAQ;IACRC,uBAAuB;IACvBC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAET,QAAQ,IAAI,UAAU,CAAC;IACtCU,QAAQ,EAAE,CAAC,UAAU,EAAET,KAAK,IAAI,OAAO,EAAEC,aAAa,IAAI,eAAe,EAAEC,WAAW,IAAI,aAAa,EAAEC,WAAW,IAAI,aAAa,CAAC;IACtIO,aAAa,EAAE,CAAC,eAAe,EAAEX,QAAQ,IAAI,uBAAuB,EAAEK,QAAQ,IAAI,UAAU,CAAC;IAC7FO,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,aAAa,EAAE,CAAC,eAAe,EAAEP,uBAAuB,IAAI,yBAAyB,CAAC;IACtFH,WAAW,EAAE,CAACA,WAAW,IAAI,aAAa,CAAC;IAC3CD,aAAa,EAAE,CAACA,aAAa,IAAI,eAAe;EAClD,CAAC;EACD,OAAO3C,cAAc,CAACiD,KAAK,EAAEnC,mBAAmB,EAAEkC,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMO,QAAQ,GAAGpD,MAAM,CAAC,KAAK,EAAE;EAC7BqD,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJpB;IACF,CAAC,GAAGmB,KAAK;IACT,OAAO,CAAC;MACN,CAAC,MAAM9C,WAAW,CAACyC,aAAa,EAAE,GAAGM,MAAM,CAACN;IAC9C,CAAC,EAAE;MACD,CAAC,MAAMzC,WAAW,CAACyC,aAAa,EAAE,GAAGd,UAAU,CAACO,uBAAuB,IAAIa,MAAM,CAACb;IACpF,CAAC,EAAEa,MAAM,CAACV,IAAI,EAAEV,UAAU,CAACC,QAAQ,IAAImB,MAAM,CAACnB,QAAQ,CAAC;EACzD;AACF,CAAC,CAAC,CAAC,CAAC;EACFD,UAAU;EACVqB;AACF,CAAC,KAAKpE,QAAQ,CAAC;EACbqE,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,EAAE;EACb;EACAC,uBAAuB,EAAE,OAAO;EAChCC,OAAO,EAAE;AACX,CAAC,EAAEzB,UAAU,CAACC,QAAQ,IAAI;EACxByB,aAAa,EAAE;AACjB,CAAC,EAAE1B,UAAU,CAACO,uBAAuB,IAAI;EACvC,CAAC,MAAMlC,WAAW,CAACyC,aAAa,EAAE,GAAG;IACnC,CAACO,KAAK,CAACM,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;MAC9BH,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC,CAAC;AACH,MAAMI,YAAY,GAAGlE,MAAM,CAAC,KAAK,EAAE;EACjCqD,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJpB;IACF,CAAC,GAAGmB,KAAK;IACT,OAAO,CAACC,MAAM,CAACT,QAAQ,EAAEX,UAAU,CAACE,KAAK,IAAIkB,MAAM,CAAClB,KAAK,EAAEF,UAAU,CAACG,aAAa,IAAIiB,MAAM,CAACjB,aAAa,EAAEH,UAAU,CAACI,WAAW,IAAIgB,MAAM,CAAChB,WAAW,EAAEJ,UAAU,CAACK,WAAW,IAAIe,MAAM,CAACf,WAAW,CAAC;EAC1M;AACF,CAAC,CAAC,CAAC,CAAC;EACFL;AACF,CAAC,KAAK/C,QAAQ,CAAC;EACb6E,QAAQ,EAAE,UAAU;EACpBL,OAAO,EAAE,cAAc;EACvBM,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;AACd,CAAC,EAAEhC,UAAU,CAACE,KAAK,IAAI;EACrB+B,SAAS,EAAE,QAAQ;EACnBC,KAAK,EAAE;AACT,CAAC,EAAElC,UAAU,CAACG,aAAa,IAAI;EAC7B;EACAgC,cAAc,EAAE,MAAM;EACtB;EACA,sBAAsB,EAAE;IACtBV,OAAO,EAAE,MAAM,CAAC;EAClB;AACF,CAAC,EAAEzB,UAAU,CAACI,WAAW,IAAI;EAC3B6B,SAAS,EAAE,MAAM;EACjBG,SAAS,EAAE;AACb,CAAC,EAAEpC,UAAU,CAACK,WAAW,IAAI;EAC3B+B,SAAS,EAAE,MAAM;EACjBH,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AACH,MAAMI,aAAa,GAAG1E,MAAM,CAAC,KAAK,EAAE;EAClCqD,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,eAAe;EACrBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJpB;IACF,CAAC,GAAGmB,KAAK;IACT,OAAO,CAACC,MAAM,CAACR,aAAa,EAAEZ,UAAU,CAACC,QAAQ,IAAImB,MAAM,CAACkB,qBAAqB,EAAEtC,UAAU,CAACM,QAAQ,IAAIc,MAAM,CAACd,QAAQ,CAAC;EAC5H;AACF,CAAC,CAAC,CAAC,CAAC;EACFN;AACF,CAAC,KAAK/C,QAAQ,CAAC;EACbwE,OAAO,EAAE;AACX,CAAC,EAAEzB,UAAU,CAACC,QAAQ,IAAI;EACxByB,aAAa,EAAE;AACjB,CAAC,EAAE1B,UAAU,CAACM,QAAQ,IAAI;EACxBiC,cAAc,EAAE;AAClB,CAAC,CAAC,CAAC;AACH,MAAMC,aAAa,GAAG7E,MAAM,CAAC,MAAM,EAAE;EACnCqD,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,WAAW;EACjBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFb,UAAU;EACVqB;AACF,CAAC,KAAKpE,QAAQ,CAAC;EACb6E,QAAQ,EAAE,UAAU;EACpBW,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTR,KAAK,EAAE,MAAM;EACbS,UAAU,EAAEtB,KAAK,CAACuB,WAAW,CAACC,MAAM,CAAC;AACvC,CAAC,EAAE7C,UAAU,CAAC8C,cAAc,KAAK,SAAS,IAAI;EAC5CC,eAAe,EAAE,CAAC1B,KAAK,CAAC2B,IAAI,IAAI3B,KAAK,EAAE4B,OAAO,CAACC,OAAO,CAACC;AACzD,CAAC,EAAEnD,UAAU,CAAC8C,cAAc,KAAK,WAAW,IAAI;EAC9CC,eAAe,EAAE,CAAC1B,KAAK,CAAC2B,IAAI,IAAI3B,KAAK,EAAE4B,OAAO,CAACG,SAAS,CAACD;AAC3D,CAAC,EAAEnD,UAAU,CAACC,QAAQ,IAAI;EACxBwC,MAAM,EAAE,MAAM;EACdP,KAAK,EAAE,CAAC;EACRmB,KAAK,EAAE;AACT,CAAC,CAAC,CAAC;AACH,MAAMC,iBAAiB,GAAG3F,MAAM,CAACO,aAAa,CAAC,CAAC;EAC9C+D,SAAS,EAAE,MAAM;EACjBG,SAAS,EAAE,QAAQ;EACnB;EACAD,cAAc,EAAE,MAAM;EACtB;EACA,sBAAsB,EAAE;IACtBV,OAAO,EAAE,MAAM,CAAC;EAClB;AACF,CAAC,CAAC;AACF,MAAM8B,qBAAqB,GAAG,CAAC,CAAC;AAChC,IAAIC,oBAAoB,GAAG,KAAK;AAChC,MAAMC,IAAI,GAAG,aAAatG,KAAK,CAACuG,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAMzC,KAAK,GAAGvD,eAAe,CAAC;IAC5BuD,KAAK,EAAEwC,OAAO;IACd3C,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMK,KAAK,GAAGxD,QAAQ,CAAC,CAAC;EACxB,MAAMgG,KAAK,GAAGpG,MAAM,CAAC,CAAC;EACtB,MAAM;MACF,YAAY,EAAEqG,SAAS;MACvB,iBAAiB,EAAEC,cAAc;MACjCC,MAAM;MACN1D,QAAQ,GAAG,KAAK;MAChB2D,QAAQ,EAAEC,YAAY;MACtBC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBC,wBAAwB,GAAG,KAAK;MAChCvB,cAAc,GAAG,SAAS;MAC1BwB,QAAQ;MACRC,WAAW,GAAG,YAAY;MAC1BC,qBAAqB,GAAGrG,eAAe;MACvC2C,aAAa,GAAG,MAAM;MACtB2D,qBAAqB;MACrBhE,KAAK,GAAG,CAAC,CAAC;MACViE,SAAS,GAAG,CAAC,CAAC;MACdC,iBAAiB,GAAG,CAAC,CAAC;MACtBC,oBAAoB,GAAG,CAAC,CAAC;MACzBC,SAAS,GAAG,SAAS;MACrBC,KAAK;MACLC,OAAO,GAAG,UAAU;MACpBC,gBAAgB,GAAG;IACrB,CAAC,GAAG7D,KAAK;IACT8D,KAAK,GAAGjI,6BAA6B,CAACmE,KAAK,EAAEjE,SAAS,CAAC;EACzD,MAAMgI,UAAU,GAAGH,OAAO,KAAK,YAAY;EAC3C,MAAM9E,QAAQ,GAAGsE,WAAW,KAAK,UAAU;EAC3C,MAAMY,WAAW,GAAGlF,QAAQ,GAAG,WAAW,GAAG,YAAY;EACzD,MAAMmF,KAAK,GAAGnF,QAAQ,GAAG,KAAK,GAAG,MAAM;EACvC,MAAMoF,GAAG,GAAGpF,QAAQ,GAAG,QAAQ,GAAG,OAAO;EACzC,MAAMqF,UAAU,GAAGrF,QAAQ,GAAG,cAAc,GAAG,aAAa;EAC5D,MAAMsF,IAAI,GAAGtF,QAAQ,GAAG,QAAQ,GAAG,OAAO;EAC1C,MAAMD,UAAU,GAAG/C,QAAQ,CAAC,CAAC,CAAC,EAAEkE,KAAK,EAAE;IACrCiD,SAAS;IACTC,wBAAwB;IACxBvB,cAAc;IACdyB,WAAW;IACXtE,QAAQ;IACRa,aAAa;IACb+D,SAAS;IACTE,OAAO;IACPC,gBAAgB;IAChB9E,KAAK,EAAE,CAACgF,UAAU;IAClB/E,aAAa,EAAE+E,UAAU,IAAI,CAACF,gBAAgB;IAC9C5E,WAAW,EAAE8E,UAAU,IAAI,CAACjF,QAAQ;IACpCI,WAAW,EAAE6E,UAAU,IAAIjF,QAAQ;IACnCK,QAAQ,EAAEA,QAAQ,IAAI,CAAC4E,UAAU;IACjC3E,uBAAuB,EAAE,CAAC8D;EAC5B,CAAC,CAAC;EACF,MAAM7D,OAAO,GAAGT,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMwF,0BAA0B,GAAG9H,YAAY,CAAC;IAC9C+H,WAAW,EAAEhF,KAAK,CAACiF,qBAAqB;IACxCC,iBAAiB,EAAEjB,SAAS,CAACkB,qBAAqB;IAClD5F;EACF,CAAC,CAAC;EACF,MAAM6F,wBAAwB,GAAGnI,YAAY,CAAC;IAC5C+H,WAAW,EAAEhF,KAAK,CAACqF,mBAAmB;IACtCH,iBAAiB,EAAEjB,SAAS,CAACqB,mBAAmB;IAChD/F;EACF,CAAC,CAAC;EACF,IAAIgG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI5F,QAAQ,IAAI4E,UAAU,EAAE;MAC1BiB,OAAO,CAACC,KAAK,CAAC,mFAAmF,GAAG,yCAAyC,CAAC;IAChJ;EACF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnJ,KAAK,CAACoJ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtJ,KAAK,CAACoJ,QAAQ,CAAChD,qBAAqB,CAAC;EACjF,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxJ,KAAK,CAACoJ,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1J,KAAK,CAACoJ,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACO,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5J,KAAK,CAACoJ,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAG9J,KAAK,CAACoJ,QAAQ,CAAC;IACvDjF,QAAQ,EAAE,QAAQ;IAClBa,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM+E,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B,MAAMC,OAAO,GAAGjK,KAAK,CAACkK,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,UAAU,GAAGnK,KAAK,CAACkK,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,QAAQ,GAAGJ,OAAO,CAACK,OAAO;IAChC,IAAIC,QAAQ;IACZ,IAAIF,QAAQ,EAAE;MACZ,MAAMG,IAAI,GAAGH,QAAQ,CAACI,qBAAqB,CAAC,CAAC;MAC7C;MACAF,QAAQ,GAAG;QACTG,WAAW,EAAEL,QAAQ,CAACK,WAAW;QACjCC,UAAU,EAAEN,QAAQ,CAACM,UAAU;QAC/BC,SAAS,EAAEP,QAAQ,CAACO,SAAS;QAC7BC,oBAAoB,EAAEjK,uBAAuB,CAACyJ,QAAQ,EAAE3D,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;QAC9EoE,WAAW,EAAET,QAAQ,CAACS,WAAW;QACjCC,GAAG,EAAEP,IAAI,CAACO,GAAG;QACbxF,MAAM,EAAEiF,IAAI,CAACjF,MAAM;QACnByF,IAAI,EAAER,IAAI,CAACQ,IAAI;QACf9E,KAAK,EAAEsE,IAAI,CAACtE;MACd,CAAC;IACH;IACA,IAAI+E,OAAO;IACX,IAAIZ,QAAQ,IAAI1C,KAAK,KAAK,KAAK,EAAE;MAC/B,MAAMb,QAAQ,GAAGqD,UAAU,CAACG,OAAO,CAACxD,QAAQ;MAC5C,IAAIA,QAAQ,CAACoE,MAAM,GAAG,CAAC,EAAE;QACvB,MAAMC,GAAG,GAAGrE,QAAQ,CAACiD,YAAY,CAACqB,GAAG,CAACzD,KAAK,CAAC,CAAC;QAC7C,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAI,CAACoC,GAAG,EAAE;YACRnC,OAAO,CAACC,KAAK,CAAC,CAAC,+DAA+D,EAAE,0CAA0CtB,KAAK,IAAI,EAAEoC,YAAY,CAACsB,IAAI,GAAG,gDAAgDC,KAAK,CAACC,IAAI,CAACxB,YAAY,CAACsB,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC;UAC5Q;QACF;QACAP,OAAO,GAAGE,GAAG,GAAGA,GAAG,CAACV,qBAAqB,CAAC,CAAC,GAAG,IAAI;QAClD,IAAI5B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAIF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAI,CAAC1C,oBAAoB,IAAI4E,OAAO,IAAIA,OAAO,CAAClG,KAAK,KAAK,CAAC,IAAIkG,OAAO,CAAC3F,MAAM,KAAK,CAAC;UACtH;UACAiF,QAAQ,CAACG,WAAW,KAAK,CAAC,EAAE;YAC1BH,QAAQ,GAAG,IAAI;YACfvB,OAAO,CAACC,KAAK,CAAC,CAAC,6DAA6D,EAAE,iCAAiCtB,KAAK,wCAAwC,EAAE,qFAAqF,CAAC,CAAC6D,IAAI,CAAC,IAAI,CAAC,CAAC;YAChQnF,oBAAoB,GAAG,IAAI;UAC7B;QACF;MACF;IACF;IACA,OAAO;MACLkE,QAAQ;MACRU;IACF,CAAC;EACH,CAAC;EACD,MAAMQ,oBAAoB,GAAGxK,gBAAgB,CAAC,MAAM;IAClD,MAAM;MACJsJ,QAAQ;MACRU;IACF,CAAC,GAAGb,WAAW,CAAC,CAAC;IACjB,IAAIsB,UAAU,GAAG,CAAC;IAClB,IAAIC,cAAc;IAClB,IAAI7I,QAAQ,EAAE;MACZ6I,cAAc,GAAG,KAAK;MACtB,IAAIV,OAAO,IAAIV,QAAQ,EAAE;QACvBmB,UAAU,GAAGT,OAAO,CAACF,GAAG,GAAGR,QAAQ,CAACQ,GAAG,GAAGR,QAAQ,CAACK,SAAS;MAC9D;IACF,CAAC,MAAM;MACLe,cAAc,GAAGjF,KAAK,GAAG,OAAO,GAAG,MAAM;MACzC,IAAIuE,OAAO,IAAIV,QAAQ,EAAE;QACvB,MAAMqB,UAAU,GAAGlF,KAAK,GAAG6D,QAAQ,CAACM,oBAAoB,GAAGN,QAAQ,CAACG,WAAW,GAAGH,QAAQ,CAACO,WAAW,GAAGP,QAAQ,CAACI,UAAU;QAC5He,UAAU,GAAG,CAAChF,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAKuE,OAAO,CAACU,cAAc,CAAC,GAAGpB,QAAQ,CAACoB,cAAc,CAAC,GAAGC,UAAU,CAAC;MACnG;IACF;IACA,MAAMC,iBAAiB,GAAG;MACxB,CAACF,cAAc,GAAGD,UAAU;MAC5B;MACA,CAACtD,IAAI,GAAG6C,OAAO,GAAGA,OAAO,CAAC7C,IAAI,CAAC,GAAG;IACpC,CAAC;;IAED;IACA;IACA,IAAI0D,KAAK,CAACzC,cAAc,CAACsC,cAAc,CAAC,CAAC,IAAIG,KAAK,CAACzC,cAAc,CAACjB,IAAI,CAAC,CAAC,EAAE;MACxEkB,iBAAiB,CAACuC,iBAAiB,CAAC;IACtC,CAAC,MAAM;MACL,MAAME,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC5C,cAAc,CAACsC,cAAc,CAAC,GAAGE,iBAAiB,CAACF,cAAc,CAAC,CAAC;MAC3F,MAAMO,KAAK,GAAGF,IAAI,CAACC,GAAG,CAAC5C,cAAc,CAACjB,IAAI,CAAC,GAAGyD,iBAAiB,CAACzD,IAAI,CAAC,CAAC;MACtE,IAAI2D,MAAM,IAAI,CAAC,IAAIG,KAAK,IAAI,CAAC,EAAE;QAC7B5C,iBAAiB,CAACuC,iBAAiB,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EACF,MAAMM,MAAM,GAAGA,CAACC,WAAW,EAAE;IAC3BC,SAAS,GAAG;EACd,CAAC,GAAG,CAAC,CAAC,KAAK;IACT,IAAIA,SAAS,EAAE;MACbvL,OAAO,CAACkH,WAAW,EAAEiC,OAAO,CAACK,OAAO,EAAE8B,WAAW,EAAE;QACjDE,QAAQ,EAAEpI,KAAK,CAACuB,WAAW,CAAC6G,QAAQ,CAACC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLtC,OAAO,CAACK,OAAO,CAACtC,WAAW,CAAC,GAAGoE,WAAW;IAC5C;EACF,CAAC;EACD,MAAMI,cAAc,GAAGC,KAAK,IAAI;IAC9B,IAAIL,WAAW,GAAGnC,OAAO,CAACK,OAAO,CAACtC,WAAW,CAAC;IAC9C,IAAIlF,QAAQ,EAAE;MACZsJ,WAAW,IAAIK,KAAK;IACtB,CAAC,MAAM;MACLL,WAAW,IAAIK,KAAK,IAAI/F,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACvC;MACA0F,WAAW,IAAI1F,KAAK,IAAI7F,gBAAgB,CAAC,CAAC,KAAK,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC;IACnE;IACAsL,MAAM,CAACC,WAAW,CAAC;EACrB,CAAC;EACD,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,aAAa,GAAG1C,OAAO,CAACK,OAAO,CAACnC,UAAU,CAAC;IACjD,IAAIyE,SAAS,GAAG,CAAC;IACjB,MAAM9F,QAAQ,GAAGwE,KAAK,CAACC,IAAI,CAACpB,UAAU,CAACG,OAAO,CAACxD,QAAQ,CAAC;IACxD,KAAK,IAAI+F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/F,QAAQ,CAACoE,MAAM,EAAE2B,CAAC,IAAI,CAAC,EAAE;MAC3C,MAAM1B,GAAG,GAAGrE,QAAQ,CAAC+F,CAAC,CAAC;MACvB,IAAID,SAAS,GAAGzB,GAAG,CAAChD,UAAU,CAAC,GAAGwE,aAAa,EAAE;QAC/C;QACA;QACA,IAAIE,CAAC,KAAK,CAAC,EAAE;UACXD,SAAS,GAAGD,aAAa;QAC3B;QACA;MACF;MACAC,SAAS,IAAIzB,GAAG,CAAChD,UAAU,CAAC;IAC9B;IACA,OAAOyE,SAAS;EAClB,CAAC;EACD,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnCN,cAAc,CAAC,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;EACtC,CAAC;EACD,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjCP,cAAc,CAACE,aAAa,CAAC,CAAC,CAAC;EACjC,CAAC;;EAED;EACA;EACA,MAAMM,yBAAyB,GAAGhN,KAAK,CAACiN,WAAW,CAACjI,cAAc,IAAI;IACpE8E,gBAAgB,CAAC;MACf3F,QAAQ,EAAE,IAAI;MACda;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAMkI,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;IAC9BA,mBAAmB,CAACC,qBAAqB,GAAGrF,UAAU,GAAG,aAAaxG,IAAI,CAAC4E,iBAAiB,EAAE;MAC5FgB,QAAQ,EAAE6F,yBAAyB;MACnChG,SAAS,EAAE7G,IAAI,CAACkD,OAAO,CAACJ,WAAW,EAAEI,OAAO,CAACL,aAAa;IAC5D,CAAC,CAAC,GAAG,IAAI;IACT,MAAMqK,mBAAmB,GAAG9D,kBAAkB,IAAIE,gBAAgB;IAClE,MAAM6D,iBAAiB,GAAGvF,UAAU,KAAKpE,aAAa,KAAK,MAAM,IAAI0J,mBAAmB,IAAI1J,aAAa,KAAK,IAAI,CAAC;IACnHwJ,mBAAmB,CAACI,iBAAiB,GAAGD,iBAAiB,GAAG,aAAa/L,IAAI,CAAC8F,qBAAqB,EAAEvH,QAAQ,CAAC;MAC5GwD,KAAK,EAAE;QACLiF,qBAAqB,EAAEjF,KAAK,CAACiF;MAC/B,CAAC;MACDhB,SAAS,EAAE;QACTkB,qBAAqB,EAAEJ;MACzB,CAAC;MACDjB,WAAW,EAAEA,WAAW;MACxBoG,SAAS,EAAE9G,KAAK,GAAG,OAAO,GAAG,MAAM;MACnC+G,OAAO,EAAEX,sBAAsB;MAC/BtK,QAAQ,EAAE,CAAC+G;IACb,CAAC,EAAE9B,oBAAoB,EAAE;MACvBT,SAAS,EAAE7G,IAAI,CAACkD,OAAO,CAACM,aAAa,EAAE8D,oBAAoB,CAACT,SAAS;IACvE,CAAC,CAAC,CAAC,GAAG,IAAI;IACVmG,mBAAmB,CAACO,eAAe,GAAGJ,iBAAiB,GAAG,aAAa/L,IAAI,CAAC8F,qBAAqB,EAAEvH,QAAQ,CAAC;MAC1GwD,KAAK,EAAE;QACLqF,mBAAmB,EAAErF,KAAK,CAACqF;MAC7B,CAAC;MACDpB,SAAS,EAAE;QACTqB,mBAAmB,EAAEF;MACvB,CAAC;MACDtB,WAAW,EAAEA,WAAW;MACxBoG,SAAS,EAAE9G,KAAK,GAAG,MAAM,GAAG,OAAO;MACnC+G,OAAO,EAAEV,oBAAoB;MAC7BvK,QAAQ,EAAE,CAACiH;IACb,CAAC,EAAEhC,oBAAoB,EAAE;MACvBT,SAAS,EAAE7G,IAAI,CAACkD,OAAO,CAACM,aAAa,EAAE8D,oBAAoB,CAACT,SAAS;IACvE,CAAC,CAAC,CAAC,GAAG,IAAI;IACV,OAAOmG,mBAAmB;EAC5B,CAAC;EACD,MAAMQ,sBAAsB,GAAG1M,gBAAgB,CAACoL,SAAS,IAAI;IAC3D,MAAM;MACJ9B,QAAQ;MACRU;IACF,CAAC,GAAGb,WAAW,CAAC,CAAC;IACjB,IAAI,CAACa,OAAO,IAAI,CAACV,QAAQ,EAAE;MACzB;IACF;IACA,IAAIU,OAAO,CAAChD,KAAK,CAAC,GAAGsC,QAAQ,CAACtC,KAAK,CAAC,EAAE;MACpC;MACA,MAAM2F,eAAe,GAAGrD,QAAQ,CAACvC,WAAW,CAAC,IAAIiD,OAAO,CAAChD,KAAK,CAAC,GAAGsC,QAAQ,CAACtC,KAAK,CAAC,CAAC;MAClFkE,MAAM,CAACyB,eAAe,EAAE;QACtBvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIpB,OAAO,CAAC/C,GAAG,CAAC,GAAGqC,QAAQ,CAACrC,GAAG,CAAC,EAAE;MACvC;MACA,MAAM0F,eAAe,GAAGrD,QAAQ,CAACvC,WAAW,CAAC,IAAIiD,OAAO,CAAC/C,GAAG,CAAC,GAAGqC,QAAQ,CAACrC,GAAG,CAAC,CAAC;MAC9EiE,MAAM,CAACyB,eAAe,EAAE;QACtBvB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAMwB,uBAAuB,GAAG5M,gBAAgB,CAAC,MAAM;IACrD,IAAI8G,UAAU,IAAIpE,aAAa,KAAK,KAAK,EAAE;MACzCiG,uBAAuB,CAAC,CAACD,oBAAoB,CAAC;IAChD;EACF,CAAC,CAAC;EACF3J,KAAK,CAAC8N,SAAS,CAAC,MAAM;IACpB,MAAMC,YAAY,GAAGpN,QAAQ,CAAC,MAAM;MAClC;MACA;MACA;MACA;MACA;MACA;MACA,IAAIsJ,OAAO,CAACK,OAAO,EAAE;QACnBmB,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IACF,IAAIuC,cAAc;;IAElB;AACJ;AACA;IACI,MAAMC,cAAc,GAAGC,OAAO,IAAI;MAChCA,OAAO,CAACC,OAAO,CAACC,MAAM,IAAI;QACxBA,MAAM,CAACC,YAAY,CAACF,OAAO,CAACvM,IAAI,IAAI;UAClC,IAAI0M,eAAe;UACnB,CAACA,eAAe,GAAGN,cAAc,KAAK,IAAI,IAAIM,eAAe,CAACC,SAAS,CAAC3M,IAAI,CAAC;QAC/E,CAAC,CAAC;QACFwM,MAAM,CAACI,UAAU,CAACL,OAAO,CAACvM,IAAI,IAAI;UAChC,IAAI6M,gBAAgB;UACpB,CAACA,gBAAgB,GAAGT,cAAc,KAAK,IAAI,IAAIS,gBAAgB,CAACC,OAAO,CAAC9M,IAAI,CAAC;QAC/E,CAAC,CAAC;MACJ,CAAC,CAAC;MACFmM,YAAY,CAAC,CAAC;MACdF,uBAAuB,CAAC,CAAC;IAC3B,CAAC;IACD,MAAMc,GAAG,GAAGtN,WAAW,CAAC4I,OAAO,CAACK,OAAO,CAAC;IACxCqE,GAAG,CAACC,gBAAgB,CAAC,QAAQ,EAAEb,YAAY,CAAC;IAC5C,IAAIc,gBAAgB;IACpB,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;MACzCd,cAAc,GAAG,IAAIc,cAAc,CAACf,YAAY,CAAC;MACjDzC,KAAK,CAACC,IAAI,CAACpB,UAAU,CAACG,OAAO,CAACxD,QAAQ,CAAC,CAACqH,OAAO,CAACY,KAAK,IAAI;QACvDf,cAAc,CAACU,OAAO,CAACK,KAAK,CAAC;MAC/B,CAAC,CAAC;IACJ;IACA,IAAI,OAAOC,gBAAgB,KAAK,WAAW,EAAE;MAC3CH,gBAAgB,GAAG,IAAIG,gBAAgB,CAACf,cAAc,CAAC;MACvDY,gBAAgB,CAACH,OAAO,CAACvE,UAAU,CAACG,OAAO,EAAE;QAC3C2E,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;IACA,OAAO,MAAM;MACX,IAAIC,iBAAiB,EAAEC,gBAAgB;MACvCpB,YAAY,CAACqB,KAAK,CAAC,CAAC;MACpBT,GAAG,CAACU,mBAAmB,CAAC,QAAQ,EAAEtB,YAAY,CAAC;MAC/C,CAACmB,iBAAiB,GAAGL,gBAAgB,KAAK,IAAI,IAAIK,iBAAiB,CAACI,UAAU,CAAC,CAAC;MAChF,CAACH,gBAAgB,GAAGnB,cAAc,KAAK,IAAI,IAAImB,gBAAgB,CAACG,UAAU,CAAC,CAAC;IAC9E,CAAC;EACH,CAAC,EAAE,CAAC7D,oBAAoB,EAAEoC,uBAAuB,CAAC,CAAC;;EAEnD;AACF;AACA;AACA;EACE7N,KAAK,CAAC8N,SAAS,CAAC,MAAM;IACpB,MAAMyB,eAAe,GAAGjE,KAAK,CAACC,IAAI,CAACpB,UAAU,CAACG,OAAO,CAACxD,QAAQ,CAAC;IAC/D,MAAMoE,MAAM,GAAGqE,eAAe,CAACrE,MAAM;IACrC,IAAI,OAAOsE,oBAAoB,KAAK,WAAW,IAAItE,MAAM,GAAG,CAAC,IAAInD,UAAU,IAAIpE,aAAa,KAAK,KAAK,EAAE;MACtG,MAAM8L,QAAQ,GAAGF,eAAe,CAAC,CAAC,CAAC;MACnC,MAAMG,OAAO,GAAGH,eAAe,CAACrE,MAAM,GAAG,CAAC,CAAC;MAC3C,MAAMyE,eAAe,GAAG;QACtBpM,IAAI,EAAE0G,OAAO,CAACK,OAAO;QACrBsF,SAAS,EAAE;MACb,CAAC;MACD,MAAMC,uBAAuB,GAAGC,OAAO,IAAI;QACzCtG,qBAAqB,CAAC,CAACsG,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC;MACnD,CAAC;MACD,MAAMC,aAAa,GAAG,IAAIR,oBAAoB,CAACK,uBAAuB,EAAEF,eAAe,CAAC;MACxFK,aAAa,CAACtB,OAAO,CAACe,QAAQ,CAAC;MAC/B,MAAMQ,qBAAqB,GAAGH,OAAO,IAAI;QACvCpG,mBAAmB,CAAC,CAACoG,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC;MACjD,CAAC;MACD,MAAMG,YAAY,GAAG,IAAIV,oBAAoB,CAACS,qBAAqB,EAAEN,eAAe,CAAC;MACrFO,YAAY,CAACxB,OAAO,CAACgB,OAAO,CAAC;MAC7B,OAAO,MAAM;QACXM,aAAa,CAACV,UAAU,CAAC,CAAC;QAC1BY,YAAY,CAACZ,UAAU,CAAC,CAAC;MAC3B,CAAC;IACH;IACA,OAAOa,SAAS;EAClB,CAAC,EAAE,CAACpI,UAAU,EAAEpE,aAAa,EAAEgG,oBAAoB,EAAE5C,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACmE,MAAM,CAAC,CAAC;EAC1GlL,KAAK,CAAC8N,SAAS,CAAC,MAAM;IACpB3E,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACNnJ,KAAK,CAAC8N,SAAS,CAAC,MAAM;IACpBrC,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACFzL,KAAK,CAAC8N,SAAS,CAAC,MAAM;IACpB;IACAH,sBAAsB,CAACvH,qBAAqB,KAAKiD,cAAc,CAAC;EAClE,CAAC,EAAE,CAACsE,sBAAsB,EAAEtE,cAAc,CAAC,CAAC;EAC5CrJ,KAAK,CAACoQ,mBAAmB,CAACvJ,MAAM,EAAE,OAAO;IACvCwJ,eAAe,EAAE5E,oBAAoB;IACrC6E,mBAAmB,EAAEzC;EACvB,CAAC,CAAC,EAAE,CAACpC,oBAAoB,EAAEoC,uBAAuB,CAAC,CAAC;EACpD,MAAMnK,SAAS,GAAG,aAAanC,IAAI,CAAC8D,aAAa,EAAEvF,QAAQ,CAAC,CAAC,CAAC,EAAE0H,iBAAiB,EAAE;IACjFR,SAAS,EAAE7G,IAAI,CAACkD,OAAO,CAACK,SAAS,EAAE8D,iBAAiB,CAACR,SAAS,CAAC;IAC/DnE,UAAU,EAAEA,UAAU;IACtB0N,KAAK,EAAEzQ,QAAQ,CAAC,CAAC,CAAC,EAAEuJ,cAAc,EAAE7B,iBAAiB,CAAC+I,KAAK;EAC7D,CAAC,CAAC,CAAC;EACH,IAAIC,UAAU,GAAG,CAAC;EAClB,MAAM1J,QAAQ,GAAG9G,KAAK,CAACyQ,QAAQ,CAACC,GAAG,CAAC3J,YAAY,EAAEgI,KAAK,IAAI;IACzD,IAAI,EAAE,aAAa/O,KAAK,CAAC2Q,cAAc,CAAC5B,KAAK,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;IACA,IAAIlG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI9I,UAAU,CAAC8O,KAAK,CAAC,EAAE;QACrB/F,OAAO,CAACC,KAAK,CAAC,CAAC,+DAA+D,EAAE,sCAAsC,CAAC,CAACuC,IAAI,CAAC,IAAI,CAAC,CAAC;MACrI;IACF;IACA,MAAMoF,UAAU,GAAG7B,KAAK,CAAC/K,KAAK,CAAC2D,KAAK,KAAKwI,SAAS,GAAGK,UAAU,GAAGzB,KAAK,CAAC/K,KAAK,CAAC2D,KAAK;IACnFoC,YAAY,CAAC8G,GAAG,CAACD,UAAU,EAAEJ,UAAU,CAAC;IACxC,MAAMM,QAAQ,GAAGF,UAAU,KAAKjJ,KAAK;IACrC6I,UAAU,IAAI,CAAC;IACf,OAAO,aAAaxQ,KAAK,CAAC+Q,YAAY,CAAChC,KAAK,EAAEjP,QAAQ,CAAC;MACrDkR,SAAS,EAAEpJ,OAAO,KAAK,WAAW;MAClClE,SAAS,EAAEoN,QAAQ,IAAI,CAAC5H,OAAO,IAAIxF,SAAS;MAC5CoN,QAAQ;MACRxJ,qBAAqB;MACrBH,QAAQ;MACRO,SAAS;MACTC,KAAK,EAAEiJ;IACT,CAAC,EAAEJ,UAAU,KAAK,CAAC,IAAI7I,KAAK,KAAK,KAAK,IAAI,CAACoH,KAAK,CAAC/K,KAAK,CAACiN,QAAQ,GAAG;MAChEA,QAAQ,EAAE;IACZ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGC,KAAK,IAAI;IAC7B,MAAMxP,IAAI,GAAGwI,UAAU,CAACG,OAAO;IAC/B,MAAMnI,YAAY,GAAGf,aAAa,CAACO,IAAI,CAAC,CAACyP,aAAa;IACtD;IACA;IACA;IACA,MAAMC,IAAI,GAAGlP,YAAY,CAACM,YAAY,CAAC,MAAM,CAAC;IAC9C,IAAI4O,IAAI,KAAK,KAAK,EAAE;MAClB;IACF;IACA,IAAIC,eAAe,GAAGlK,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,SAAS;IAC5E,IAAImK,WAAW,GAAGnK,WAAW,KAAK,YAAY,GAAG,YAAY,GAAG,WAAW;IAC3E,IAAIA,WAAW,KAAK,YAAY,IAAIV,KAAK,EAAE;MACzC;MACA4K,eAAe,GAAG,YAAY;MAC9BC,WAAW,GAAG,WAAW;IAC3B;IACA,QAAQJ,KAAK,CAACK,GAAG;MACf,KAAKF,eAAe;QAClBH,KAAK,CAACM,cAAc,CAAC,CAAC;QACtBvP,SAAS,CAACP,IAAI,EAAEQ,YAAY,EAAEJ,YAAY,CAAC;QAC3C;MACF,KAAKwP,WAAW;QACdJ,KAAK,CAACM,cAAc,CAAC,CAAC;QACtBvP,SAAS,CAACP,IAAI,EAAEQ,YAAY,EAAET,QAAQ,CAAC;QACvC;MACF,KAAK,MAAM;QACTyP,KAAK,CAACM,cAAc,CAAC,CAAC;QACtBvP,SAAS,CAACP,IAAI,EAAE,IAAI,EAAED,QAAQ,CAAC;QAC/B;MACF,KAAK,KAAK;QACRyP,KAAK,CAACM,cAAc,CAAC,CAAC;QACtBvP,SAAS,CAACP,IAAI,EAAE,IAAI,EAAEI,YAAY,CAAC;QACnC;MACF;QACE;IACJ;EACF,CAAC;EACD,MAAMoL,mBAAmB,GAAGD,sBAAsB,CAAC,CAAC;EACpD,OAAO,aAAazL,KAAK,CAACmC,QAAQ,EAAE9D,QAAQ,CAAC;IAC3CkH,SAAS,EAAE7G,IAAI,CAACkD,OAAO,CAACE,IAAI,EAAEyD,SAAS,CAAC;IACxCnE,UAAU,EAAEA,UAAU;IACtB4D,GAAG,EAAEA,GAAG;IACRiL,EAAE,EAAEzK;EACN,CAAC,EAAEa,KAAK,EAAE;IACRhB,QAAQ,EAAE,CAACqG,mBAAmB,CAACI,iBAAiB,EAAEJ,mBAAmB,CAACC,qBAAqB,EAAE,aAAa3L,KAAK,CAACiD,YAAY,EAAE;MAC5HsC,SAAS,EAAE3D,OAAO,CAACG,QAAQ;MAC3BX,UAAU,EAAEA,UAAU;MACtB0N,KAAK,EAAE;QACLpM,QAAQ,EAAE0F,aAAa,CAAC1F,QAAQ;QAChC,CAACrB,QAAQ,GAAG,SAAS4D,KAAK,GAAG,MAAM,GAAG,OAAO,EAAE,GAAG,cAAc,GAAGmB,gBAAgB,GAAGsI,SAAS,GAAG,CAACtG,aAAa,CAAC7E;MACnH,CAAC;MACDyB,GAAG,EAAEwD,OAAO;MACZnD,QAAQ,EAAE,CAAC,aAAavF,IAAI,CAAC2D,aAAa,EAAE;QAC1C,YAAY,EAAEyB,SAAS;QACvB,iBAAiB,EAAEC,cAAc;QACjC,kBAAkB,EAAEQ,WAAW,KAAK,UAAU,GAAG,UAAU,GAAG,IAAI;QAClEJ,SAAS,EAAE3D,OAAO,CAACI,aAAa;QAChCZ,UAAU,EAAEA,UAAU;QACtB8O,SAAS,EAAET,aAAa;QACxBzK,GAAG,EAAE0D,UAAU;QACfkH,IAAI,EAAE,SAAS;QACfvK,QAAQ,EAAEA;MACZ,CAAC,CAAC,EAAEoC,OAAO,IAAIxF,SAAS;IAC1B,CAAC,CAAC,EAAEyJ,mBAAmB,CAACO,eAAe;EACzC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF7E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzC,IAAI,CAACsL,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE/K,MAAM,EAAEzG,OAAO;EACf;AACF;AACA;AACA;AACA;EACE8G,wBAAwB,EAAEhH,SAAS,CAAC2R,IAAI;EACxC;AACF;AACA;EACE,YAAY,EAAE3R,SAAS,CAAC4R,MAAM;EAC9B;AACF;AACA;EACE,iBAAiB,EAAE5R,SAAS,CAAC4R,MAAM;EACnC;AACF;AACA;AACA;AACA;EACE3O,QAAQ,EAAEjD,SAAS,CAAC2R,IAAI;EACxB;AACF;AACA;EACE/K,QAAQ,EAAE5G,SAAS,CAAC6R,IAAI;EACxB;AACF;AACA;EACE1O,OAAO,EAAEnD,SAAS,CAAC8R,MAAM;EACzB;AACF;AACA;EACEhL,SAAS,EAAE9G,SAAS,CAAC4R,MAAM;EAC3B;AACF;AACA;AACA;EACE7K,SAAS,EAAE/G,SAAS,CAACoI,WAAW;EAChC;AACF;AACA;AACA;EACE3C,cAAc,EAAEzF,SAAS,CAAC,sCAAsC+R,SAAS,CAAC,CAAC/R,SAAS,CAACgS,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,EAAEhS,SAAS,CAAC4R,MAAM,CAAC,CAAC;EACxI;AACF;AACA;AACA;AACA;AACA;EACE3K,QAAQ,EAAEjH,SAAS,CAACiS,IAAI;EACxB;AACF;AACA;AACA;EACE/K,WAAW,EAAElH,SAAS,CAACgS,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;AACA;EACE7K,qBAAqB,EAAEnH,SAAS,CAACoI,WAAW;EAC5C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3E,aAAa,EAAEzD,SAAS,CAAC,sCAAsCgS,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EAC3F;AACF;AACA;AACA;EACE5K,qBAAqB,EAAEpH,SAAS,CAAC2R,IAAI;EACrC;AACF;AACA;AACA;AACA;EACEtK,SAAS,EAAErH,SAAS,CAACkS,KAAK,CAAC;IACzBxJ,mBAAmB,EAAE1I,SAAS,CAAC+R,SAAS,CAAC,CAAC/R,SAAS,CAACiS,IAAI,EAAEjS,SAAS,CAAC8R,MAAM,CAAC,CAAC;IAC5EvJ,qBAAqB,EAAEvI,SAAS,CAAC+R,SAAS,CAAC,CAAC/R,SAAS,CAACiS,IAAI,EAAEjS,SAAS,CAAC8R,MAAM,CAAC;EAC/E,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE1O,KAAK,EAAEpD,SAAS,CAACkS,KAAK,CAAC;IACrBzJ,mBAAmB,EAAEzI,SAAS,CAACoI,WAAW;IAC1CC,qBAAqB,EAAErI,SAAS,CAACoI;EACnC,CAAC,CAAC;EACF;AACF;AACA;EACE+J,EAAE,EAAEnS,SAAS,CAAC+R,SAAS,CAAC,CAAC/R,SAAS,CAACoS,OAAO,CAACpS,SAAS,CAAC+R,SAAS,CAAC,CAAC/R,SAAS,CAACiS,IAAI,EAAEjS,SAAS,CAAC8R,MAAM,EAAE9R,SAAS,CAAC2R,IAAI,CAAC,CAAC,CAAC,EAAE3R,SAAS,CAACiS,IAAI,EAAEjS,SAAS,CAAC8R,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACExK,iBAAiB,EAAEtH,SAAS,CAAC8R,MAAM;EACnC;AACF;AACA;AACA;EACEvK,oBAAoB,EAAEvH,SAAS,CAAC8R,MAAM;EACtC;AACF;AACA;AACA;EACEtK,SAAS,EAAExH,SAAS,CAACgS,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;EAC/D;AACF;AACA;AACA;EACEvK,KAAK,EAAEzH,SAAS,CAACqS,GAAG;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3K,OAAO,EAAE1H,SAAS,CAACgS,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;EACjE;AACF;AACA;AACA;AACA;EACErK,gBAAgB,EAAE3H,SAAS,CAAC2R;AAC9B,CAAC,GAAG,KAAK,CAAC;AACV,eAAevL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}