{"ast": null, "code": "import requirePropFactory from '@mui/utils/requirePropFactory';\nexport default requirePropFactory;", "map": {"version": 3, "names": ["requirePropFactory"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/utils/requirePropFactory.js"], "sourcesContent": ["import requirePropFactory from '@mui/utils/requirePropFactory';\nexport default requirePropFactory;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,+BAA+B;AAC9D,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}