{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"ref\"],\n  _excluded2 = [\"ariaLabel\", \"FabProps\", \"children\", \"className\", \"direction\", \"hidden\", \"icon\", \"onBlur\", \"onClose\", \"onFocus\", \"onKeyDown\", \"onMouseEnter\", \"onMouseLeave\", \"onOpen\", \"open\", \"openIcon\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\"],\n  _excluded3 = [\"ref\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useTimeout from '@mui/utils/useTimeout';\nimport clamp from '@mui/utils/clamp';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport Zoom from '../Zoom';\nimport Fab from '../Fab';\nimport capitalize from '../utils/capitalize';\nimport isMuiElement from '../utils/isMuiElement';\nimport useForkRef from '../utils/useForkRef';\nimport useControlled from '../utils/useControlled';\nimport speedDialClasses, { getSpeedDialUtilityClass } from './speedDialClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    open,\n    direction\n  } = ownerState;\n  const slots = {\n    root: ['root', `direction${capitalize(direction)}`],\n    fab: ['fab'],\n    actions: ['actions', !open && 'actionsClosed']\n  };\n  return composeClasses(slots, getSpeedDialUtilityClass, classes);\n};\nfunction getOrientation(direction) {\n  if (direction === 'up' || direction === 'down') {\n    return 'vertical';\n  }\n  if (direction === 'right' || direction === 'left') {\n    return 'horizontal';\n  }\n  return undefined;\n}\nconst dialRadius = 32;\nconst spacingActions = 16;\nconst SpeedDialRoot = styled('div', {\n  name: 'MuiSpeedDial',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`direction${capitalize(ownerState.direction)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  zIndex: (theme.vars || theme).zIndex.speedDial,\n  display: 'flex',\n  alignItems: 'center',\n  pointerEvents: 'none'\n}, ownerState.direction === 'up' && {\n  flexDirection: 'column-reverse',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'column-reverse',\n    marginBottom: -dialRadius,\n    paddingBottom: spacingActions + dialRadius\n  }\n}, ownerState.direction === 'down' && {\n  flexDirection: 'column',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'column',\n    marginTop: -dialRadius,\n    paddingTop: spacingActions + dialRadius\n  }\n}, ownerState.direction === 'left' && {\n  flexDirection: 'row-reverse',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'row-reverse',\n    marginRight: -dialRadius,\n    paddingRight: spacingActions + dialRadius\n  }\n}, ownerState.direction === 'right' && {\n  flexDirection: 'row',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'row',\n    marginLeft: -dialRadius,\n    paddingLeft: spacingActions + dialRadius\n  }\n}));\nconst SpeedDialFab = styled(Fab, {\n  name: 'MuiSpeedDial',\n  slot: 'Fab',\n  overridesResolver: (props, styles) => styles.fab\n})(() => ({\n  pointerEvents: 'auto'\n}));\nconst SpeedDialActions = styled('div', {\n  name: 'MuiSpeedDial',\n  slot: 'Actions',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.actions, !ownerState.open && styles.actionsClosed];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex',\n  pointerEvents: 'auto'\n}, !ownerState.open && {\n  transition: 'top 0s linear 0.2s',\n  pointerEvents: 'none'\n}));\nconst SpeedDial = /*#__PURE__*/React.forwardRef(function SpeedDial(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSpeedDial'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      ariaLabel,\n      FabProps: {\n        ref: origDialButtonRef\n      } = {},\n      children: childrenProp,\n      className,\n      direction = 'up',\n      hidden = false,\n      icon,\n      onBlur,\n      onClose,\n      onFocus,\n      onKeyDown,\n      onMouseEnter,\n      onMouseLeave,\n      onOpen,\n      open: openProp,\n      TransitionComponent = Zoom,\n      transitionDuration = defaultTransitionDuration,\n      TransitionProps\n    } = props,\n    FabProps = _objectWithoutPropertiesLoose(props.FabProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'SpeedDial',\n    state: 'open'\n  });\n  const ownerState = _extends({}, props, {\n    open,\n    direction\n  });\n  const classes = useUtilityClasses(ownerState);\n  const eventTimer = useTimeout();\n\n  /**\n   * an index in actions.current\n   */\n  const focusedAction = React.useRef(0);\n\n  /**\n   * pressing this key while the focus is on a child SpeedDialAction focuses\n   * the next SpeedDialAction.\n   * It is equal to the first arrow key pressed while focus is on the SpeedDial\n   * that is not orthogonal to the direction.\n   * @type {utils.ArrowKey?}\n   */\n  const nextItemArrowKey = React.useRef();\n\n  /**\n   * refs to the Button that have an action associated to them in this SpeedDial\n   * [Fab, ...(SpeedDialActions > Button)]\n   * @type {HTMLButtonElement[]}\n   */\n  const actions = React.useRef([]);\n  actions.current = [actions.current[0]];\n  const handleOwnFabRef = React.useCallback(fabFef => {\n    actions.current[0] = fabFef;\n  }, []);\n  const handleFabRef = useForkRef(origDialButtonRef, handleOwnFabRef);\n\n  /**\n   * creates a ref callback for the Button in a SpeedDialAction\n   * Is called before the original ref callback for Button that was set in buttonProps\n   *\n   * @param dialActionIndex {number}\n   * @param origButtonRef {React.RefObject?}\n   */\n  const createHandleSpeedDialActionButtonRef = (dialActionIndex, origButtonRef) => {\n    return buttonRef => {\n      actions.current[dialActionIndex + 1] = buttonRef;\n      if (origButtonRef) {\n        origButtonRef(buttonRef);\n      }\n    };\n  };\n  const handleKeyDown = event => {\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n    const key = event.key.replace('Arrow', '').toLowerCase();\n    const {\n      current: nextItemArrowKeyCurrent = key\n    } = nextItemArrowKey;\n    if (event.key === 'Escape') {\n      setOpenState(false);\n      actions.current[0].focus();\n      if (onClose) {\n        onClose(event, 'escapeKeyDown');\n      }\n      return;\n    }\n    if (getOrientation(key) === getOrientation(nextItemArrowKeyCurrent) && getOrientation(key) !== undefined) {\n      event.preventDefault();\n      const actionStep = key === nextItemArrowKeyCurrent ? 1 : -1;\n\n      // stay within array indices\n      const nextAction = clamp(focusedAction.current + actionStep, 0, actions.current.length - 1);\n      actions.current[nextAction].focus();\n      focusedAction.current = nextAction;\n      nextItemArrowKey.current = nextItemArrowKeyCurrent;\n    }\n  };\n  React.useEffect(() => {\n    // actions were closed while navigation state was not reset\n    if (!open) {\n      focusedAction.current = 0;\n      nextItemArrowKey.current = undefined;\n    }\n  }, [open]);\n  const handleClose = event => {\n    if (event.type === 'mouseleave' && onMouseLeave) {\n      onMouseLeave(event);\n    }\n    if (event.type === 'blur' && onBlur) {\n      onBlur(event);\n    }\n    eventTimer.clear();\n    if (event.type === 'blur') {\n      eventTimer.start(0, () => {\n        setOpenState(false);\n        if (onClose) {\n          onClose(event, 'blur');\n        }\n      });\n    } else {\n      setOpenState(false);\n      if (onClose) {\n        onClose(event, 'mouseLeave');\n      }\n    }\n  };\n  const handleClick = event => {\n    if (FabProps.onClick) {\n      FabProps.onClick(event);\n    }\n    eventTimer.clear();\n    if (open) {\n      setOpenState(false);\n      if (onClose) {\n        onClose(event, 'toggle');\n      }\n    } else {\n      setOpenState(true);\n      if (onOpen) {\n        onOpen(event, 'toggle');\n      }\n    }\n  };\n  const handleOpen = event => {\n    if (event.type === 'mouseenter' && onMouseEnter) {\n      onMouseEnter(event);\n    }\n    if (event.type === 'focus' && onFocus) {\n      onFocus(event);\n    }\n\n    // When moving the focus between two items,\n    // a chain if blur and focus event is triggered.\n    // We only handle the last event.\n    eventTimer.clear();\n    if (!open) {\n      // Wait for a future focus or click event\n      eventTimer.start(0, () => {\n        setOpenState(true);\n        if (onOpen) {\n          const eventMap = {\n            focus: 'focus',\n            mouseenter: 'mouseEnter'\n          };\n          onOpen(event, eventMap[event.type]);\n        }\n      });\n    }\n  };\n\n  // Filter the label for valid id characters.\n  const id = ariaLabel.replace(/^[^a-z]+|[^\\w:.-]+/gi, '');\n  const allItems = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The SpeedDial component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const children = allItems.map((child, index) => {\n    const _child$props = child.props,\n      {\n        FabProps: {\n          ref: origButtonRef\n        } = {},\n        tooltipPlacement: tooltipPlacementProp\n      } = _child$props,\n      ChildFabProps = _objectWithoutPropertiesLoose(_child$props.FabProps, _excluded3);\n    const tooltipPlacement = tooltipPlacementProp || (getOrientation(direction) === 'vertical' ? 'left' : 'top');\n    return /*#__PURE__*/React.cloneElement(child, {\n      FabProps: _extends({}, ChildFabProps, {\n        ref: createHandleSpeedDialActionButtonRef(index, origButtonRef)\n      }),\n      delay: 30 * (open ? index : allItems.length - index),\n      open,\n      tooltipPlacement,\n      id: `${id}-action-${index}`\n    });\n  });\n  return /*#__PURE__*/_jsxs(SpeedDialRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    role: \"presentation\",\n    onKeyDown: handleKeyDown,\n    onBlur: handleClose,\n    onFocus: handleOpen,\n    onMouseEnter: handleOpen,\n    onMouseLeave: handleClose,\n    ownerState: ownerState\n  }, other, {\n    children: [/*#__PURE__*/_jsx(TransitionComponent, _extends({\n      in: !hidden,\n      timeout: transitionDuration,\n      unmountOnExit: true\n    }, TransitionProps, {\n      children: /*#__PURE__*/_jsx(SpeedDialFab, _extends({\n        color: \"primary\",\n        \"aria-label\": ariaLabel,\n        \"aria-haspopup\": \"true\",\n        \"aria-expanded\": open,\n        \"aria-controls\": `${id}-actions`\n      }, FabProps, {\n        onClick: handleClick,\n        className: clsx(classes.fab, FabProps.className),\n        ref: handleFabRef,\n        ownerState: ownerState,\n        children: /*#__PURE__*/React.isValidElement(icon) && isMuiElement(icon, ['SpeedDialIcon']) ? /*#__PURE__*/React.cloneElement(icon, {\n          open\n        }) : icon\n      }))\n    })), /*#__PURE__*/_jsx(SpeedDialActions, {\n      id: `${id}-actions`,\n      role: \"menu\",\n      \"aria-orientation\": getOrientation(direction),\n      className: clsx(classes.actions, !open && classes.actionsClosed),\n      ownerState: ownerState,\n      children: children\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SpeedDial.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The aria-label of the button element.\n   * Also used to provide the `id` for the `SpeedDial` element and its children.\n   */\n  ariaLabel: PropTypes.string.isRequired,\n  /**\n   * SpeedDialActions to display when the SpeedDial is `open`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The direction the actions open relative to the floating action button.\n   * @default 'up'\n   */\n  direction: PropTypes.oneOf(['down', 'left', 'right', 'up']),\n  /**\n   * Props applied to the [`Fab`](/material-ui/api/fab/) element.\n   * @default {}\n   */\n  FabProps: PropTypes.object,\n  /**\n   * If `true`, the SpeedDial is hidden.\n   * @default false\n   */\n  hidden: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Fab. The `SpeedDialIcon` component\n   * provides a default Icon with animation.\n   */\n  icon: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggle\"`, `\"blur\"`, `\"mouseLeave\"`, `\"escapeKeyDown\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggle\"`, `\"focus\"`, `\"mouseEnter\"`.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Fab when the SpeedDial is open.\n   */\n  openIcon: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Zoom\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default SpeedDial;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "_excluded3", "React", "isFragment", "PropTypes", "clsx", "composeClasses", "useTimeout", "clamp", "styled", "useDefaultProps", "useTheme", "Zoom", "Fab", "capitalize", "isMuiElement", "useForkRef", "useControlled", "speedDialClasses", "getSpeedDialUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "open", "direction", "slots", "root", "fab", "actions", "getOrientation", "undefined", "dialRadius", "spacingActions", "SpeedDialRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "zIndex", "vars", "speedDial", "display", "alignItems", "pointerEvents", "flexDirection", "marginBottom", "paddingBottom", "marginTop", "paddingTop", "marginRight", "paddingRight", "marginLeft", "paddingLeft", "SpeedDialFab", "SpeedDialActions", "actionsClosed", "transition", "SpeedDial", "forwardRef", "inProps", "ref", "defaultTransitionDuration", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "aria<PERSON><PERSON><PERSON>", "FabProps", "origDialButtonRef", "children", "childrenProp", "className", "hidden", "icon", "onBlur", "onClose", "onFocus", "onKeyDown", "onMouseEnter", "onMouseLeave", "onOpen", "openProp", "TransitionComponent", "transitionDuration", "TransitionProps", "other", "setOpenState", "controlled", "default", "state", "eventTimer", "focusedAction", "useRef", "nextItemArrowKey", "current", "handleOwnFabRef", "useCallback", "fabFef", "handleFabRef", "createHandleSpeedDialActionButtonRef", "dialActionIndex", "origButtonRef", "buttonRef", "handleKeyDown", "event", "key", "replace", "toLowerCase", "nextItemArrowKeyCurrent", "focus", "preventDefault", "actionStep", "nextAction", "length", "useEffect", "handleClose", "type", "clear", "start", "handleClick", "onClick", "handleOpen", "eventMap", "mouseenter", "id", "allItems", "Children", "toArray", "filter", "child", "process", "env", "NODE_ENV", "console", "error", "join", "isValidElement", "map", "index", "_child$props", "tooltipPlacement", "tooltipPlacementProp", "ChildFabProps", "cloneElement", "delay", "role", "in", "timeout", "unmountOnExit", "color", "propTypes", "string", "isRequired", "node", "object", "oneOf", "bool", "func", "openIcon", "sx", "oneOfType", "arrayOf", "elementType", "number", "shape", "appear"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/SpeedDial/SpeedDial.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"ref\"],\n  _excluded2 = [\"ariaLabel\", \"FabProps\", \"children\", \"className\", \"direction\", \"hidden\", \"icon\", \"onBlur\", \"onClose\", \"onFocus\", \"onKeyDown\", \"onMouseEnter\", \"onMouseLeave\", \"onOpen\", \"open\", \"openIcon\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\"],\n  _excluded3 = [\"ref\"];\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useTimeout from '@mui/utils/useTimeout';\nimport clamp from '@mui/utils/clamp';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport Zoom from '../Zoom';\nimport Fab from '../Fab';\nimport capitalize from '../utils/capitalize';\nimport isMuiElement from '../utils/isMuiElement';\nimport useForkRef from '../utils/useForkRef';\nimport useControlled from '../utils/useControlled';\nimport speedDialClasses, { getSpeedDialUtilityClass } from './speedDialClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    open,\n    direction\n  } = ownerState;\n  const slots = {\n    root: ['root', `direction${capitalize(direction)}`],\n    fab: ['fab'],\n    actions: ['actions', !open && 'actionsClosed']\n  };\n  return composeClasses(slots, getSpeedDialUtilityClass, classes);\n};\nfunction getOrientation(direction) {\n  if (direction === 'up' || direction === 'down') {\n    return 'vertical';\n  }\n  if (direction === 'right' || direction === 'left') {\n    return 'horizontal';\n  }\n  return undefined;\n}\nconst dialRadius = 32;\nconst spacingActions = 16;\nconst SpeedDialRoot = styled('div', {\n  name: 'MuiSpeedDial',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`direction${capitalize(ownerState.direction)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  zIndex: (theme.vars || theme).zIndex.speedDial,\n  display: 'flex',\n  alignItems: 'center',\n  pointerEvents: 'none'\n}, ownerState.direction === 'up' && {\n  flexDirection: 'column-reverse',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'column-reverse',\n    marginBottom: -dialRadius,\n    paddingBottom: spacingActions + dialRadius\n  }\n}, ownerState.direction === 'down' && {\n  flexDirection: 'column',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'column',\n    marginTop: -dialRadius,\n    paddingTop: spacingActions + dialRadius\n  }\n}, ownerState.direction === 'left' && {\n  flexDirection: 'row-reverse',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'row-reverse',\n    marginRight: -dialRadius,\n    paddingRight: spacingActions + dialRadius\n  }\n}, ownerState.direction === 'right' && {\n  flexDirection: 'row',\n  [`& .${speedDialClasses.actions}`]: {\n    flexDirection: 'row',\n    marginLeft: -dialRadius,\n    paddingLeft: spacingActions + dialRadius\n  }\n}));\nconst SpeedDialFab = styled(Fab, {\n  name: 'MuiSpeedDial',\n  slot: 'Fab',\n  overridesResolver: (props, styles) => styles.fab\n})(() => ({\n  pointerEvents: 'auto'\n}));\nconst SpeedDialActions = styled('div', {\n  name: 'MuiSpeedDial',\n  slot: 'Actions',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.actions, !ownerState.open && styles.actionsClosed];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex',\n  pointerEvents: 'auto'\n}, !ownerState.open && {\n  transition: 'top 0s linear 0.2s',\n  pointerEvents: 'none'\n}));\nconst SpeedDial = /*#__PURE__*/React.forwardRef(function SpeedDial(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSpeedDial'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      ariaLabel,\n      FabProps: {\n        ref: origDialButtonRef\n      } = {},\n      children: childrenProp,\n      className,\n      direction = 'up',\n      hidden = false,\n      icon,\n      onBlur,\n      onClose,\n      onFocus,\n      onKeyDown,\n      onMouseEnter,\n      onMouseLeave,\n      onOpen,\n      open: openProp,\n      TransitionComponent = Zoom,\n      transitionDuration = defaultTransitionDuration,\n      TransitionProps\n    } = props,\n    FabProps = _objectWithoutPropertiesLoose(props.FabProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'SpeedDial',\n    state: 'open'\n  });\n  const ownerState = _extends({}, props, {\n    open,\n    direction\n  });\n  const classes = useUtilityClasses(ownerState);\n  const eventTimer = useTimeout();\n\n  /**\n   * an index in actions.current\n   */\n  const focusedAction = React.useRef(0);\n\n  /**\n   * pressing this key while the focus is on a child SpeedDialAction focuses\n   * the next SpeedDialAction.\n   * It is equal to the first arrow key pressed while focus is on the SpeedDial\n   * that is not orthogonal to the direction.\n   * @type {utils.ArrowKey?}\n   */\n  const nextItemArrowKey = React.useRef();\n\n  /**\n   * refs to the Button that have an action associated to them in this SpeedDial\n   * [Fab, ...(SpeedDialActions > Button)]\n   * @type {HTMLButtonElement[]}\n   */\n  const actions = React.useRef([]);\n  actions.current = [actions.current[0]];\n  const handleOwnFabRef = React.useCallback(fabFef => {\n    actions.current[0] = fabFef;\n  }, []);\n  const handleFabRef = useForkRef(origDialButtonRef, handleOwnFabRef);\n\n  /**\n   * creates a ref callback for the Button in a SpeedDialAction\n   * Is called before the original ref callback for Button that was set in buttonProps\n   *\n   * @param dialActionIndex {number}\n   * @param origButtonRef {React.RefObject?}\n   */\n  const createHandleSpeedDialActionButtonRef = (dialActionIndex, origButtonRef) => {\n    return buttonRef => {\n      actions.current[dialActionIndex + 1] = buttonRef;\n      if (origButtonRef) {\n        origButtonRef(buttonRef);\n      }\n    };\n  };\n  const handleKeyDown = event => {\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n    const key = event.key.replace('Arrow', '').toLowerCase();\n    const {\n      current: nextItemArrowKeyCurrent = key\n    } = nextItemArrowKey;\n    if (event.key === 'Escape') {\n      setOpenState(false);\n      actions.current[0].focus();\n      if (onClose) {\n        onClose(event, 'escapeKeyDown');\n      }\n      return;\n    }\n    if (getOrientation(key) === getOrientation(nextItemArrowKeyCurrent) && getOrientation(key) !== undefined) {\n      event.preventDefault();\n      const actionStep = key === nextItemArrowKeyCurrent ? 1 : -1;\n\n      // stay within array indices\n      const nextAction = clamp(focusedAction.current + actionStep, 0, actions.current.length - 1);\n      actions.current[nextAction].focus();\n      focusedAction.current = nextAction;\n      nextItemArrowKey.current = nextItemArrowKeyCurrent;\n    }\n  };\n  React.useEffect(() => {\n    // actions were closed while navigation state was not reset\n    if (!open) {\n      focusedAction.current = 0;\n      nextItemArrowKey.current = undefined;\n    }\n  }, [open]);\n  const handleClose = event => {\n    if (event.type === 'mouseleave' && onMouseLeave) {\n      onMouseLeave(event);\n    }\n    if (event.type === 'blur' && onBlur) {\n      onBlur(event);\n    }\n    eventTimer.clear();\n    if (event.type === 'blur') {\n      eventTimer.start(0, () => {\n        setOpenState(false);\n        if (onClose) {\n          onClose(event, 'blur');\n        }\n      });\n    } else {\n      setOpenState(false);\n      if (onClose) {\n        onClose(event, 'mouseLeave');\n      }\n    }\n  };\n  const handleClick = event => {\n    if (FabProps.onClick) {\n      FabProps.onClick(event);\n    }\n    eventTimer.clear();\n    if (open) {\n      setOpenState(false);\n      if (onClose) {\n        onClose(event, 'toggle');\n      }\n    } else {\n      setOpenState(true);\n      if (onOpen) {\n        onOpen(event, 'toggle');\n      }\n    }\n  };\n  const handleOpen = event => {\n    if (event.type === 'mouseenter' && onMouseEnter) {\n      onMouseEnter(event);\n    }\n    if (event.type === 'focus' && onFocus) {\n      onFocus(event);\n    }\n\n    // When moving the focus between two items,\n    // a chain if blur and focus event is triggered.\n    // We only handle the last event.\n    eventTimer.clear();\n    if (!open) {\n      // Wait for a future focus or click event\n      eventTimer.start(0, () => {\n        setOpenState(true);\n        if (onOpen) {\n          const eventMap = {\n            focus: 'focus',\n            mouseenter: 'mouseEnter'\n          };\n          onOpen(event, eventMap[event.type]);\n        }\n      });\n    }\n  };\n\n  // Filter the label for valid id characters.\n  const id = ariaLabel.replace(/^[^a-z]+|[^\\w:.-]+/gi, '');\n  const allItems = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The SpeedDial component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const children = allItems.map((child, index) => {\n    const _child$props = child.props,\n      {\n        FabProps: {\n          ref: origButtonRef\n        } = {},\n        tooltipPlacement: tooltipPlacementProp\n      } = _child$props,\n      ChildFabProps = _objectWithoutPropertiesLoose(_child$props.FabProps, _excluded3);\n    const tooltipPlacement = tooltipPlacementProp || (getOrientation(direction) === 'vertical' ? 'left' : 'top');\n    return /*#__PURE__*/React.cloneElement(child, {\n      FabProps: _extends({}, ChildFabProps, {\n        ref: createHandleSpeedDialActionButtonRef(index, origButtonRef)\n      }),\n      delay: 30 * (open ? index : allItems.length - index),\n      open,\n      tooltipPlacement,\n      id: `${id}-action-${index}`\n    });\n  });\n  return /*#__PURE__*/_jsxs(SpeedDialRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    role: \"presentation\",\n    onKeyDown: handleKeyDown,\n    onBlur: handleClose,\n    onFocus: handleOpen,\n    onMouseEnter: handleOpen,\n    onMouseLeave: handleClose,\n    ownerState: ownerState\n  }, other, {\n    children: [/*#__PURE__*/_jsx(TransitionComponent, _extends({\n      in: !hidden,\n      timeout: transitionDuration,\n      unmountOnExit: true\n    }, TransitionProps, {\n      children: /*#__PURE__*/_jsx(SpeedDialFab, _extends({\n        color: \"primary\",\n        \"aria-label\": ariaLabel,\n        \"aria-haspopup\": \"true\",\n        \"aria-expanded\": open,\n        \"aria-controls\": `${id}-actions`\n      }, FabProps, {\n        onClick: handleClick,\n        className: clsx(classes.fab, FabProps.className),\n        ref: handleFabRef,\n        ownerState: ownerState,\n        children: /*#__PURE__*/React.isValidElement(icon) && isMuiElement(icon, ['SpeedDialIcon']) ? /*#__PURE__*/React.cloneElement(icon, {\n          open\n        }) : icon\n      }))\n    })), /*#__PURE__*/_jsx(SpeedDialActions, {\n      id: `${id}-actions`,\n      role: \"menu\",\n      \"aria-orientation\": getOrientation(direction),\n      className: clsx(classes.actions, !open && classes.actionsClosed),\n      ownerState: ownerState,\n      children: children\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SpeedDial.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The aria-label of the button element.\n   * Also used to provide the `id` for the `SpeedDial` element and its children.\n   */\n  ariaLabel: PropTypes.string.isRequired,\n  /**\n   * SpeedDialActions to display when the SpeedDial is `open`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The direction the actions open relative to the floating action button.\n   * @default 'up'\n   */\n  direction: PropTypes.oneOf(['down', 'left', 'right', 'up']),\n  /**\n   * Props applied to the [`Fab`](/material-ui/api/fab/) element.\n   * @default {}\n   */\n  FabProps: PropTypes.object,\n  /**\n   * If `true`, the SpeedDial is hidden.\n   * @default false\n   */\n  hidden: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Fab. The `SpeedDialIcon` component\n   * provides a default Icon with animation.\n   */\n  icon: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggle\"`, `\"blur\"`, `\"mouseLeave\"`, `\"escapeKeyDown\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggle\"`, `\"focus\"`, `\"mouseEnter\"`.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Fab when the SpeedDial is open.\n   */\n  openIcon: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Zoom\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default SpeedDial;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,KAAK,CAAC;EACvBC,UAAU,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;EACzQC,UAAU,GAAG,CAAC,KAAK,CAAC;AACtB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,GAAG,MAAM,QAAQ;AACxB,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,gBAAgB,IAAIC,wBAAwB,QAAQ,oBAAoB;AAC/E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,YAAYhB,UAAU,CAACc,SAAS,CAAC,EAAE,CAAC;IACnDG,GAAG,EAAE,CAAC,KAAK,CAAC;IACZC,OAAO,EAAE,CAAC,SAAS,EAAE,CAACL,IAAI,IAAI,eAAe;EAC/C,CAAC;EACD,OAAOrB,cAAc,CAACuB,KAAK,EAAEV,wBAAwB,EAAEO,OAAO,CAAC;AACjE,CAAC;AACD,SAASO,cAAcA,CAACL,SAAS,EAAE;EACjC,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,MAAM,EAAE;IAC9C,OAAO,UAAU;EACnB;EACA,IAAIA,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,MAAM,EAAE;IACjD,OAAO,YAAY;EACrB;EACA,OAAOM,SAAS;AAClB;AACA,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,cAAc,GAAG,EAAE;AACzB,MAAMC,aAAa,GAAG5B,MAAM,CAAC,KAAK,EAAE;EAClC6B,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJjB;IACF,CAAC,GAAGgB,KAAK;IACT,OAAO,CAACC,MAAM,CAACZ,IAAI,EAAEY,MAAM,CAAC,YAAY5B,UAAU,CAACW,UAAU,CAACG,SAAS,CAAC,EAAE,CAAC,CAAC;EAC9E;AACF,CAAC,CAAC,CAAC,CAAC;EACFe,KAAK;EACLlB;AACF,CAAC,KAAK3B,QAAQ,CAAC;EACb8C,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACE,SAAS;EAC9CC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,aAAa,EAAE;AACjB,CAAC,EAAExB,UAAU,CAACG,SAAS,KAAK,IAAI,IAAI;EAClCsB,aAAa,EAAE,gBAAgB;EAC/B,CAAC,MAAMhC,gBAAgB,CAACc,OAAO,EAAE,GAAG;IAClCkB,aAAa,EAAE,gBAAgB;IAC/BC,YAAY,EAAE,CAAChB,UAAU;IACzBiB,aAAa,EAAEhB,cAAc,GAAGD;EAClC;AACF,CAAC,EAAEV,UAAU,CAACG,SAAS,KAAK,MAAM,IAAI;EACpCsB,aAAa,EAAE,QAAQ;EACvB,CAAC,MAAMhC,gBAAgB,CAACc,OAAO,EAAE,GAAG;IAClCkB,aAAa,EAAE,QAAQ;IACvBG,SAAS,EAAE,CAAClB,UAAU;IACtBmB,UAAU,EAAElB,cAAc,GAAGD;EAC/B;AACF,CAAC,EAAEV,UAAU,CAACG,SAAS,KAAK,MAAM,IAAI;EACpCsB,aAAa,EAAE,aAAa;EAC5B,CAAC,MAAMhC,gBAAgB,CAACc,OAAO,EAAE,GAAG;IAClCkB,aAAa,EAAE,aAAa;IAC5BK,WAAW,EAAE,CAACpB,UAAU;IACxBqB,YAAY,EAAEpB,cAAc,GAAGD;EACjC;AACF,CAAC,EAAEV,UAAU,CAACG,SAAS,KAAK,OAAO,IAAI;EACrCsB,aAAa,EAAE,KAAK;EACpB,CAAC,MAAMhC,gBAAgB,CAACc,OAAO,EAAE,GAAG;IAClCkB,aAAa,EAAE,KAAK;IACpBO,UAAU,EAAE,CAACtB,UAAU;IACvBuB,WAAW,EAAEtB,cAAc,GAAGD;EAChC;AACF,CAAC,CAAC,CAAC;AACH,MAAMwB,YAAY,GAAGlD,MAAM,CAACI,GAAG,EAAE;EAC/ByB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,KAAK;EACXC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACX;AAC/C,CAAC,CAAC,CAAC,OAAO;EACRkB,aAAa,EAAE;AACjB,CAAC,CAAC,CAAC;AACH,MAAMW,gBAAgB,GAAGnD,MAAM,CAAC,KAAK,EAAE;EACrC6B,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJjB;IACF,CAAC,GAAGgB,KAAK;IACT,OAAO,CAACC,MAAM,CAACV,OAAO,EAAE,CAACP,UAAU,CAACE,IAAI,IAAIe,MAAM,CAACmB,aAAa,CAAC;EACnE;AACF,CAAC,CAAC,CAAC,CAAC;EACFpC;AACF,CAAC,KAAK3B,QAAQ,CAAC;EACbiD,OAAO,EAAE,MAAM;EACfE,aAAa,EAAE;AACjB,CAAC,EAAE,CAACxB,UAAU,CAACE,IAAI,IAAI;EACrBmC,UAAU,EAAE,oBAAoB;EAChCb,aAAa,EAAE;AACjB,CAAC,CAAC,CAAC;AACH,MAAMc,SAAS,GAAG,aAAa7D,KAAK,CAAC8D,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,MAAMzB,KAAK,GAAG/B,eAAe,CAAC;IAC5B+B,KAAK,EAAEwB,OAAO;IACd3B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMK,KAAK,GAAGhC,QAAQ,CAAC,CAAC;EACxB,MAAMwD,yBAAyB,GAAG;IAChCC,KAAK,EAAEzB,KAAK,CAAC0B,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAE7B,KAAK,CAAC0B,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;MACFC,SAAS;MACTC,QAAQ,EAAE;QACRT,GAAG,EAAEU;MACP,CAAC,GAAG,CAAC,CAAC;MACNC,QAAQ,EAAEC,YAAY;MACtBC,SAAS;MACTnD,SAAS,GAAG,IAAI;MAChBoD,MAAM,GAAG,KAAK;MACdC,IAAI;MACJC,MAAM;MACNC,OAAO;MACPC,OAAO;MACPC,SAAS;MACTC,YAAY;MACZC,YAAY;MACZC,MAAM;MACN7D,IAAI,EAAE8D,QAAQ;MACdC,mBAAmB,GAAG9E,IAAI;MAC1B+E,kBAAkB,GAAGxB,yBAAyB;MAC9CyB;IACF,CAAC,GAAGnD,KAAK;IACTkC,QAAQ,GAAG9E,6BAA6B,CAAC4C,KAAK,CAACkC,QAAQ,EAAE5E,SAAS,CAAC;IACnE8F,KAAK,GAAGhG,6BAA6B,CAAC4C,KAAK,EAAEzC,UAAU,CAAC;EAC1D,MAAM,CAAC2B,IAAI,EAAEmE,YAAY,CAAC,GAAG7E,aAAa,CAAC;IACzC8E,UAAU,EAAEN,QAAQ;IACpBO,OAAO,EAAE,KAAK;IACd1D,IAAI,EAAE,WAAW;IACjB2D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMxE,UAAU,GAAG3B,QAAQ,CAAC,CAAC,CAAC,EAAE2C,KAAK,EAAE;IACrCd,IAAI;IACJC;EACF,CAAC,CAAC;EACF,MAAMF,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMyE,UAAU,GAAG3F,UAAU,CAAC,CAAC;;EAE/B;AACF;AACA;EACE,MAAM4F,aAAa,GAAGjG,KAAK,CAACkG,MAAM,CAAC,CAAC,CAAC;;EAErC;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,gBAAgB,GAAGnG,KAAK,CAACkG,MAAM,CAAC,CAAC;;EAEvC;AACF;AACA;AACA;AACA;EACE,MAAMpE,OAAO,GAAG9B,KAAK,CAACkG,MAAM,CAAC,EAAE,CAAC;EAChCpE,OAAO,CAACsE,OAAO,GAAG,CAACtE,OAAO,CAACsE,OAAO,CAAC,CAAC,CAAC,CAAC;EACtC,MAAMC,eAAe,GAAGrG,KAAK,CAACsG,WAAW,CAACC,MAAM,IAAI;IAClDzE,OAAO,CAACsE,OAAO,CAAC,CAAC,CAAC,GAAGG,MAAM;EAC7B,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,YAAY,GAAG1F,UAAU,CAAC4D,iBAAiB,EAAE2B,eAAe,CAAC;;EAEnE;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMI,oCAAoC,GAAGA,CAACC,eAAe,EAAEC,aAAa,KAAK;IAC/E,OAAOC,SAAS,IAAI;MAClB9E,OAAO,CAACsE,OAAO,CAACM,eAAe,GAAG,CAAC,CAAC,GAAGE,SAAS;MAChD,IAAID,aAAa,EAAE;QACjBA,aAAa,CAACC,SAAS,CAAC;MAC1B;IACF,CAAC;EACH,CAAC;EACD,MAAMC,aAAa,GAAGC,KAAK,IAAI;IAC7B,IAAI3B,SAAS,EAAE;MACbA,SAAS,CAAC2B,KAAK,CAAC;IAClB;IACA,MAAMC,GAAG,GAAGD,KAAK,CAACC,GAAG,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IACxD,MAAM;MACJb,OAAO,EAAEc,uBAAuB,GAAGH;IACrC,CAAC,GAAGZ,gBAAgB;IACpB,IAAIW,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC1BnB,YAAY,CAAC,KAAK,CAAC;MACnB9D,OAAO,CAACsE,OAAO,CAAC,CAAC,CAAC,CAACe,KAAK,CAAC,CAAC;MAC1B,IAAIlC,OAAO,EAAE;QACXA,OAAO,CAAC6B,KAAK,EAAE,eAAe,CAAC;MACjC;MACA;IACF;IACA,IAAI/E,cAAc,CAACgF,GAAG,CAAC,KAAKhF,cAAc,CAACmF,uBAAuB,CAAC,IAAInF,cAAc,CAACgF,GAAG,CAAC,KAAK/E,SAAS,EAAE;MACxG8E,KAAK,CAACM,cAAc,CAAC,CAAC;MACtB,MAAMC,UAAU,GAAGN,GAAG,KAAKG,uBAAuB,GAAG,CAAC,GAAG,CAAC,CAAC;;MAE3D;MACA,MAAMI,UAAU,GAAGhH,KAAK,CAAC2F,aAAa,CAACG,OAAO,GAAGiB,UAAU,EAAE,CAAC,EAAEvF,OAAO,CAACsE,OAAO,CAACmB,MAAM,GAAG,CAAC,CAAC;MAC3FzF,OAAO,CAACsE,OAAO,CAACkB,UAAU,CAAC,CAACH,KAAK,CAAC,CAAC;MACnClB,aAAa,CAACG,OAAO,GAAGkB,UAAU;MAClCnB,gBAAgB,CAACC,OAAO,GAAGc,uBAAuB;IACpD;EACF,CAAC;EACDlH,KAAK,CAACwH,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAAC/F,IAAI,EAAE;MACTwE,aAAa,CAACG,OAAO,GAAG,CAAC;MACzBD,gBAAgB,CAACC,OAAO,GAAGpE,SAAS;IACtC;EACF,CAAC,EAAE,CAACP,IAAI,CAAC,CAAC;EACV,MAAMgG,WAAW,GAAGX,KAAK,IAAI;IAC3B,IAAIA,KAAK,CAACY,IAAI,KAAK,YAAY,IAAIrC,YAAY,EAAE;MAC/CA,YAAY,CAACyB,KAAK,CAAC;IACrB;IACA,IAAIA,KAAK,CAACY,IAAI,KAAK,MAAM,IAAI1C,MAAM,EAAE;MACnCA,MAAM,CAAC8B,KAAK,CAAC;IACf;IACAd,UAAU,CAAC2B,KAAK,CAAC,CAAC;IAClB,IAAIb,KAAK,CAACY,IAAI,KAAK,MAAM,EAAE;MACzB1B,UAAU,CAAC4B,KAAK,CAAC,CAAC,EAAE,MAAM;QACxBhC,YAAY,CAAC,KAAK,CAAC;QACnB,IAAIX,OAAO,EAAE;UACXA,OAAO,CAAC6B,KAAK,EAAE,MAAM,CAAC;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLlB,YAAY,CAAC,KAAK,CAAC;MACnB,IAAIX,OAAO,EAAE;QACXA,OAAO,CAAC6B,KAAK,EAAE,YAAY,CAAC;MAC9B;IACF;EACF,CAAC;EACD,MAAMe,WAAW,GAAGf,KAAK,IAAI;IAC3B,IAAIrC,QAAQ,CAACqD,OAAO,EAAE;MACpBrD,QAAQ,CAACqD,OAAO,CAAChB,KAAK,CAAC;IACzB;IACAd,UAAU,CAAC2B,KAAK,CAAC,CAAC;IAClB,IAAIlG,IAAI,EAAE;MACRmE,YAAY,CAAC,KAAK,CAAC;MACnB,IAAIX,OAAO,EAAE;QACXA,OAAO,CAAC6B,KAAK,EAAE,QAAQ,CAAC;MAC1B;IACF,CAAC,MAAM;MACLlB,YAAY,CAAC,IAAI,CAAC;MAClB,IAAIN,MAAM,EAAE;QACVA,MAAM,CAACwB,KAAK,EAAE,QAAQ,CAAC;MACzB;IACF;EACF,CAAC;EACD,MAAMiB,UAAU,GAAGjB,KAAK,IAAI;IAC1B,IAAIA,KAAK,CAACY,IAAI,KAAK,YAAY,IAAItC,YAAY,EAAE;MAC/CA,YAAY,CAAC0B,KAAK,CAAC;IACrB;IACA,IAAIA,KAAK,CAACY,IAAI,KAAK,OAAO,IAAIxC,OAAO,EAAE;MACrCA,OAAO,CAAC4B,KAAK,CAAC;IAChB;;IAEA;IACA;IACA;IACAd,UAAU,CAAC2B,KAAK,CAAC,CAAC;IAClB,IAAI,CAAClG,IAAI,EAAE;MACT;MACAuE,UAAU,CAAC4B,KAAK,CAAC,CAAC,EAAE,MAAM;QACxBhC,YAAY,CAAC,IAAI,CAAC;QAClB,IAAIN,MAAM,EAAE;UACV,MAAM0C,QAAQ,GAAG;YACfb,KAAK,EAAE,OAAO;YACdc,UAAU,EAAE;UACd,CAAC;UACD3C,MAAM,CAACwB,KAAK,EAAEkB,QAAQ,CAAClB,KAAK,CAACY,IAAI,CAAC,CAAC;QACrC;MACF,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMQ,EAAE,GAAG1D,SAAS,CAACwC,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;EACxD,MAAMmB,QAAQ,GAAGnI,KAAK,CAACoI,QAAQ,CAACC,OAAO,CAACzD,YAAY,CAAC,CAAC0D,MAAM,CAACC,KAAK,IAAI;IACpE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIzI,UAAU,CAACsI,KAAK,CAAC,EAAE;QACrBI,OAAO,CAACC,KAAK,CAAC,CAAC,oEAAoE,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1I;IACF;IACA,OAAO,aAAa7I,KAAK,CAAC8I,cAAc,CAACP,KAAK,CAAC;EACjD,CAAC,CAAC;EACF,MAAM5D,QAAQ,GAAGwD,QAAQ,CAACY,GAAG,CAAC,CAACR,KAAK,EAAES,KAAK,KAAK;IAC9C,MAAMC,YAAY,GAAGV,KAAK,CAAChG,KAAK;MAC9B;QACEkC,QAAQ,EAAE;UACRT,GAAG,EAAE2C;QACP,CAAC,GAAG,CAAC,CAAC;QACNuC,gBAAgB,EAAEC;MACpB,CAAC,GAAGF,YAAY;MAChBG,aAAa,GAAGzJ,6BAA6B,CAACsJ,YAAY,CAACxE,QAAQ,EAAE1E,UAAU,CAAC;IAClF,MAAMmJ,gBAAgB,GAAGC,oBAAoB,KAAKpH,cAAc,CAACL,SAAS,CAAC,KAAK,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5G,OAAO,aAAa1B,KAAK,CAACqJ,YAAY,CAACd,KAAK,EAAE;MAC5C9D,QAAQ,EAAE7E,QAAQ,CAAC,CAAC,CAAC,EAAEwJ,aAAa,EAAE;QACpCpF,GAAG,EAAEyC,oCAAoC,CAACuC,KAAK,EAAErC,aAAa;MAChE,CAAC,CAAC;MACF2C,KAAK,EAAE,EAAE,IAAI7H,IAAI,GAAGuH,KAAK,GAAGb,QAAQ,CAACZ,MAAM,GAAGyB,KAAK,CAAC;MACpDvH,IAAI;MACJyH,gBAAgB;MAChBhB,EAAE,EAAE,GAAGA,EAAE,WAAWc,KAAK;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAa3H,KAAK,CAACc,aAAa,EAAEvC,QAAQ,CAAC;IAChDiF,SAAS,EAAE1E,IAAI,CAACqB,OAAO,CAACI,IAAI,EAAEiD,SAAS,CAAC;IACxCb,GAAG,EAAEA,GAAG;IACRuF,IAAI,EAAE,cAAc;IACpBpE,SAAS,EAAE0B,aAAa;IACxB7B,MAAM,EAAEyC,WAAW;IACnBvC,OAAO,EAAE6C,UAAU;IACnB3C,YAAY,EAAE2C,UAAU;IACxB1C,YAAY,EAAEoC,WAAW;IACzBlG,UAAU,EAAEA;EACd,CAAC,EAAEoE,KAAK,EAAE;IACRhB,QAAQ,EAAE,CAAC,aAAaxD,IAAI,CAACqE,mBAAmB,EAAE5F,QAAQ,CAAC;MACzD4J,EAAE,EAAE,CAAC1E,MAAM;MACX2E,OAAO,EAAEhE,kBAAkB;MAC3BiE,aAAa,EAAE;IACjB,CAAC,EAAEhE,eAAe,EAAE;MAClBf,QAAQ,EAAE,aAAaxD,IAAI,CAACsC,YAAY,EAAE7D,QAAQ,CAAC;QACjD+J,KAAK,EAAE,SAAS;QAChB,YAAY,EAAEnF,SAAS;QACvB,eAAe,EAAE,MAAM;QACvB,eAAe,EAAE/C,IAAI;QACrB,eAAe,EAAE,GAAGyG,EAAE;MACxB,CAAC,EAAEzD,QAAQ,EAAE;QACXqD,OAAO,EAAED,WAAW;QACpBhD,SAAS,EAAE1E,IAAI,CAACqB,OAAO,CAACK,GAAG,EAAE4C,QAAQ,CAACI,SAAS,CAAC;QAChDb,GAAG,EAAEwC,YAAY;QACjBjF,UAAU,EAAEA,UAAU;QACtBoD,QAAQ,EAAE,aAAa3E,KAAK,CAAC8I,cAAc,CAAC/D,IAAI,CAAC,IAAIlE,YAAY,CAACkE,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC,GAAG,aAAa/E,KAAK,CAACqJ,YAAY,CAACtE,IAAI,EAAE;UACjItD;QACF,CAAC,CAAC,GAAGsD;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,EAAE,aAAa5D,IAAI,CAACuC,gBAAgB,EAAE;MACvCwE,EAAE,EAAE,GAAGA,EAAE,UAAU;MACnBqB,IAAI,EAAE,MAAM;MACZ,kBAAkB,EAAExH,cAAc,CAACL,SAAS,CAAC;MAC7CmD,SAAS,EAAE1E,IAAI,CAACqB,OAAO,CAACM,OAAO,EAAE,CAACL,IAAI,IAAID,OAAO,CAACmC,aAAa,CAAC;MAChEpC,UAAU,EAAEA,UAAU;MACtBoD,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF6D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7E,SAAS,CAAC+F,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEpF,SAAS,EAAEtE,SAAS,CAAC2J,MAAM,CAACC,UAAU;EACtC;AACF;AACA;EACEnF,QAAQ,EAAEzE,SAAS,CAAC6J,IAAI;EACxB;AACF;AACA;EACEvI,OAAO,EAAEtB,SAAS,CAAC8J,MAAM;EACzB;AACF;AACA;EACEnF,SAAS,EAAE3E,SAAS,CAAC2J,MAAM;EAC3B;AACF;AACA;AACA;EACEnI,SAAS,EAAExB,SAAS,CAAC+J,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EAC3D;AACF;AACA;AACA;EACExF,QAAQ,EAAEvE,SAAS,CAAC8J,MAAM;EAC1B;AACF;AACA;AACA;EACElF,MAAM,EAAE5E,SAAS,CAACgK,IAAI;EACtB;AACF;AACA;AACA;EACEnF,IAAI,EAAE7E,SAAS,CAAC6J,IAAI;EACpB;AACF;AACA;EACE/E,MAAM,EAAE9E,SAAS,CAACiK,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;EACElF,OAAO,EAAE/E,SAAS,CAACiK,IAAI;EACvB;AACF;AACA;EACEjF,OAAO,EAAEhF,SAAS,CAACiK,IAAI;EACvB;AACF;AACA;EACEhF,SAAS,EAAEjF,SAAS,CAACiK,IAAI;EACzB;AACF;AACA;EACE/E,YAAY,EAAElF,SAAS,CAACiK,IAAI;EAC5B;AACF;AACA;EACE9E,YAAY,EAAEnF,SAAS,CAACiK,IAAI;EAC5B;AACF;AACA;AACA;AACA;AACA;EACE7E,MAAM,EAAEpF,SAAS,CAACiK,IAAI;EACtB;AACF;AACA;EACE1I,IAAI,EAAEvB,SAAS,CAACgK,IAAI;EACpB;AACF;AACA;EACEE,QAAQ,EAAElK,SAAS,CAAC6J,IAAI;EACxB;AACF;AACA;EACEM,EAAE,EAAEnK,SAAS,CAACoK,SAAS,CAAC,CAACpK,SAAS,CAACqK,OAAO,CAACrK,SAAS,CAACoK,SAAS,CAAC,CAACpK,SAAS,CAACiK,IAAI,EAAEjK,SAAS,CAAC8J,MAAM,EAAE9J,SAAS,CAACgK,IAAI,CAAC,CAAC,CAAC,EAAEhK,SAAS,CAACiK,IAAI,EAAEjK,SAAS,CAAC8J,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;EACExE,mBAAmB,EAAEtF,SAAS,CAACsK,WAAW;EAC1C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE/E,kBAAkB,EAAEvF,SAAS,CAACoK,SAAS,CAAC,CAACpK,SAAS,CAACuK,MAAM,EAAEvK,SAAS,CAACwK,KAAK,CAAC;IACzEC,MAAM,EAAEzK,SAAS,CAACuK,MAAM;IACxBvG,KAAK,EAAEhE,SAAS,CAACuK,MAAM;IACvBnG,IAAI,EAAEpE,SAAS,CAACuK;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;EACE/E,eAAe,EAAExF,SAAS,CAAC8J;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAenG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}