{"ast": null, "code": "import warning from '../utils/warning';\nfunction verify(selector, methodName) {\n  if (!selector) {\n    throw new Error(`Unexpected value for ${methodName} in connect.`);\n  } else if (methodName === 'mapStateToProps' || methodName === 'mapDispatchToProps') {\n    if (!Object.prototype.hasOwnProperty.call(selector, 'dependsOnOwnProps')) {\n      warning(`The selector for ${methodName} of connect did not specify a value for dependsOnOwnProps.`);\n    }\n  }\n}\nexport default function verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps) {\n  verify(mapStateToProps, 'mapStateToProps');\n  verify(mapDispatchToProps, 'mapDispatchToProps');\n  verify(mergeProps, 'mergeProps');\n}", "map": {"version": 3, "names": ["warning", "verify", "selector", "methodName", "Error", "Object", "prototype", "hasOwnProperty", "call", "verifySubselectors", "mapStateToProps", "mapDispatchToProps", "mergeProps"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/react-redux/es/connect/verifySubselectors.js"], "sourcesContent": ["import warning from '../utils/warning';\n\nfunction verify(selector, methodName) {\n  if (!selector) {\n    throw new Error(`Unexpected value for ${methodName} in connect.`);\n  } else if (methodName === 'mapStateToProps' || methodName === 'mapDispatchToProps') {\n    if (!Object.prototype.hasOwnProperty.call(selector, 'dependsOnOwnProps')) {\n      warning(`The selector for ${methodName} of connect did not specify a value for dependsOnOwnProps.`);\n    }\n  }\n}\n\nexport default function verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps) {\n  verify(mapStateToProps, 'mapStateToProps');\n  verify(mapDispatchToProps, 'mapDispatchToProps');\n  verify(mergeProps, 'mergeProps');\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AAEtC,SAASC,MAAMA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EACpC,IAAI,CAACD,QAAQ,EAAE;IACb,MAAM,IAAIE,KAAK,CAAC,wBAAwBD,UAAU,cAAc,CAAC;EACnE,CAAC,MAAM,IAAIA,UAAU,KAAK,iBAAiB,IAAIA,UAAU,KAAK,oBAAoB,EAAE;IAClF,IAAI,CAACE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACN,QAAQ,EAAE,mBAAmB,CAAC,EAAE;MACxEF,OAAO,CAAC,oBAAoBG,UAAU,4DAA4D,CAAC;IACrG;EACF;AACF;AAEA,eAAe,SAASM,kBAAkBA,CAACC,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAE;EAC1FX,MAAM,CAACS,eAAe,EAAE,iBAAiB,CAAC;EAC1CT,MAAM,CAACU,kBAAkB,EAAE,oBAAoB,CAAC;EAChDV,MAAM,CAACW,UAAU,EAAE,YAAY,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}