{"ast": null, "code": "'use client';\n\nexport { default } from './usePagination';", "map": {"version": 3, "names": ["default"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/usePagination/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './usePagination';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}