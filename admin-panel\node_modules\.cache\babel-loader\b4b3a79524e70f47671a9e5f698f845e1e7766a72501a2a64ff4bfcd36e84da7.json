{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableContainerUtilityClass(slot) {\n  return generateUtilityClass('MuiTableContainer', slot);\n}\nconst tableContainerClasses = generateUtilityClasses('MuiTableContainer', ['root']);\nexport default tableContainerClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTableContainerUtilityClass", "slot", "tableContainerClasses"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/TableContainer/tableContainerClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableContainerUtilityClass(slot) {\n  return generateUtilityClass('MuiTableContainer', slot);\n}\nconst tableContainerClasses = generateUtilityClasses('MuiTableContainer', ['root']);\nexport default tableContainerClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOF,oBAAoB,CAAC,mBAAmB,EAAEE,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGJ,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,CAAC,CAAC;AACnF,eAAeI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}