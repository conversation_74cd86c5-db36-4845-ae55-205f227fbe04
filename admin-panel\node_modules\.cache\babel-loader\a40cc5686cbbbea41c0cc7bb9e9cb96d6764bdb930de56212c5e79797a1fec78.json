{"ast": null, "code": "import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport * as fetchAdapter from './fetch.js';\nimport AxiosError from \"../core/AxiosError.js\";\nconst knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter,\n  fetch: {\n    get: fetchAdapter.getFetch\n  }\n};\nutils.forEach(knownAdapters, (fn, value) => {\n  if (fn) {\n    try {\n      Object.defineProperty(fn, 'name', {\n        value\n      });\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {\n      value\n    });\n  }\n});\nconst renderReason = reason => `- ${reason}`;\nconst isResolvedHandle = adapter => utils.isFunction(adapter) || adapter === null || adapter === false;\nexport default {\n  getAdapter: (adapters, config) => {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n    const {\n      length\n    } = adapters;\n    let nameOrAdapter;\n    let adapter;\n    const rejectedReasons = {};\n    for (let i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      let id;\n      adapter = nameOrAdapter;\n      if (!isResolvedHandle(nameOrAdapter)) {\n        adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];\n        if (adapter === undefined) {\n          throw new AxiosError(`Unknown adapter '${id}'`);\n        }\n      }\n      if (adapter && (utils.isFunction(adapter) || (adapter = adapter.get(config)))) {\n        break;\n      }\n      rejectedReasons[id || '#' + i] = adapter;\n    }\n    if (!adapter) {\n      const reasons = Object.entries(rejectedReasons).map(([id, state]) => `adapter ${id} ` + (state === false ? 'is not supported by the environment' : 'is not available in the build'));\n      let s = length ? reasons.length > 1 ? 'since :\\n' + reasons.map(renderReason).join('\\n') : ' ' + renderReason(reasons[0]) : 'as no adapter specified';\n      throw new AxiosError(`There is no suitable adapter to dispatch the request ` + s, 'ERR_NOT_SUPPORT');\n    }\n    return adapter;\n  },\n  adapters: knownAdapters\n};", "map": {"version": 3, "names": ["utils", "httpAdapter", "xhrAdapter", "fetchAdapter", "AxiosError", "knownAdapters", "http", "xhr", "fetch", "get", "getFetch", "for<PERSON>ach", "fn", "value", "Object", "defineProperty", "e", "renderReason", "reason", "isResolvedHandle", "adapter", "isFunction", "getAdapter", "adapters", "config", "isArray", "length", "nameOrAdapter", "rejectedReasons", "i", "id", "String", "toLowerCase", "undefined", "reasons", "entries", "map", "state", "s", "join"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/axios/lib/adapters/adapters.js"], "sourcesContent": ["import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport * as fetchAdapter from './fetch.js';\nimport AxiosError from \"../core/AxiosError.js\";\n\nconst knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter,\n  fetch: {\n    get: fetchAdapter.getFetch,\n  }\n}\n\nutils.forEach(knownAdapters, (fn, value) => {\n  if (fn) {\n    try {\n      Object.defineProperty(fn, 'name', {value});\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {value});\n  }\n});\n\nconst renderReason = (reason) => `- ${reason}`;\n\nconst isResolvedHandle = (adapter) => utils.isFunction(adapter) || adapter === null || adapter === false;\n\nexport default {\n  getAdapter: (adapters, config) => {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n\n    const {length} = adapters;\n    let nameOrAdapter;\n    let adapter;\n\n    const rejectedReasons = {};\n\n    for (let i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      let id;\n\n      adapter = nameOrAdapter;\n\n      if (!isResolvedHandle(nameOrAdapter)) {\n        adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];\n\n        if (adapter === undefined) {\n          throw new AxiosError(`Unknown adapter '${id}'`);\n        }\n      }\n\n      if (adapter && (utils.isFunction(adapter) || (adapter = adapter.get(config)))) {\n        break;\n      }\n\n      rejectedReasons[id || '#' + i] = adapter;\n    }\n\n    if (!adapter) {\n\n      const reasons = Object.entries(rejectedReasons)\n        .map(([id, state]) => `adapter ${id} ` +\n          (state === false ? 'is not supported by the environment' : 'is not available in the build')\n        );\n\n      let s = length ?\n        (reasons.length > 1 ? 'since :\\n' + reasons.map(renderReason).join('\\n') : ' ' + renderReason(reasons[0])) :\n        'as no adapter specified';\n\n      throw new AxiosError(\n        `There is no suitable adapter to dispatch the request ` + s,\n        'ERR_NOT_SUPPORT'\n      );\n    }\n\n    return adapter;\n  },\n  adapters: knownAdapters\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,UAAU,MAAM,UAAU;AACjC,OAAO,KAAKC,YAAY,MAAM,YAAY;AAC1C,OAAOC,UAAU,MAAM,uBAAuB;AAE9C,MAAMC,aAAa,GAAG;EACpBC,IAAI,EAAEL,WAAW;EACjBM,GAAG,EAAEL,UAAU;EACfM,KAAK,EAAE;IACLC,GAAG,EAAEN,YAAY,CAACO;EACpB;AACF,CAAC;AAEDV,KAAK,CAACW,OAAO,CAACN,aAAa,EAAE,CAACO,EAAE,EAAEC,KAAK,KAAK;EAC1C,IAAID,EAAE,EAAE;IACN,IAAI;MACFE,MAAM,CAACC,cAAc,CAACH,EAAE,EAAE,MAAM,EAAE;QAACC;MAAK,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV;IAAA;IAEFF,MAAM,CAACC,cAAc,CAACH,EAAE,EAAE,aAAa,EAAE;MAACC;IAAK,CAAC,CAAC;EACnD;AACF,CAAC,CAAC;AAEF,MAAMI,YAAY,GAAIC,MAAM,IAAK,KAAKA,MAAM,EAAE;AAE9C,MAAMC,gBAAgB,GAAIC,OAAO,IAAKpB,KAAK,CAACqB,UAAU,CAACD,OAAO,CAAC,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK;AAExG,eAAe;EACbE,UAAU,EAAEA,CAACC,QAAQ,EAAEC,MAAM,KAAK;IAChCD,QAAQ,GAAGvB,KAAK,CAACyB,OAAO,CAACF,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAE1D,MAAM;MAACG;IAAM,CAAC,GAAGH,QAAQ;IACzB,IAAII,aAAa;IACjB,IAAIP,OAAO;IAEX,MAAMQ,eAAe,GAAG,CAAC,CAAC;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;MAC/BF,aAAa,GAAGJ,QAAQ,CAACM,CAAC,CAAC;MAC3B,IAAIC,EAAE;MAENV,OAAO,GAAGO,aAAa;MAEvB,IAAI,CAACR,gBAAgB,CAACQ,aAAa,CAAC,EAAE;QACpCP,OAAO,GAAGf,aAAa,CAAC,CAACyB,EAAE,GAAGC,MAAM,CAACJ,aAAa,CAAC,EAAEK,WAAW,CAAC,CAAC,CAAC;QAEnE,IAAIZ,OAAO,KAAKa,SAAS,EAAE;UACzB,MAAM,IAAI7B,UAAU,CAAC,oBAAoB0B,EAAE,GAAG,CAAC;QACjD;MACF;MAEA,IAAIV,OAAO,KAAKpB,KAAK,CAACqB,UAAU,CAACD,OAAO,CAAC,KAAKA,OAAO,GAAGA,OAAO,CAACX,GAAG,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE;QAC7E;MACF;MAEAI,eAAe,CAACE,EAAE,IAAI,GAAG,GAAGD,CAAC,CAAC,GAAGT,OAAO;IAC1C;IAEA,IAAI,CAACA,OAAO,EAAE;MAEZ,MAAMc,OAAO,GAAGpB,MAAM,CAACqB,OAAO,CAACP,eAAe,CAAC,CAC5CQ,GAAG,CAAC,CAAC,CAACN,EAAE,EAAEO,KAAK,CAAC,KAAK,WAAWP,EAAE,GAAG,IACnCO,KAAK,KAAK,KAAK,GAAG,qCAAqC,GAAG,+BAA+B,CAC5F,CAAC;MAEH,IAAIC,CAAC,GAAGZ,MAAM,GACXQ,OAAO,CAACR,MAAM,GAAG,CAAC,GAAG,WAAW,GAAGQ,OAAO,CAACE,GAAG,CAACnB,YAAY,CAAC,CAACsB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAGtB,YAAY,CAACiB,OAAO,CAAC,CAAC,CAAC,CAAC,GACzG,yBAAyB;MAE3B,MAAM,IAAI9B,UAAU,CAClB,uDAAuD,GAAGkC,CAAC,EAC3D,iBACF,CAAC;IACH;IAEA,OAAOlB,OAAO;EAChB,CAAC;EACDG,QAAQ,EAAElB;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}