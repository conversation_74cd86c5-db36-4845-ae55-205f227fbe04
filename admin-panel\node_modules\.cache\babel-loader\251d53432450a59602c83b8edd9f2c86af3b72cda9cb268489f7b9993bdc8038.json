{"ast": null, "code": "import * as React from 'react';\nconst DialogContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  DialogContext.displayName = 'DialogContext';\n}\nexport default DialogContext;", "map": {"version": 3, "names": ["React", "DialogContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/Dialog/DialogContext.js"], "sourcesContent": ["import * as React from 'react';\nconst DialogContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  DialogContext.displayName = 'DialogContext';\n}\nexport default DialogContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC1D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,aAAa,CAACK,WAAW,GAAG,eAAe;AAC7C;AACA,eAAeL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}