{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"sx\"];\nimport { isPlainObject } from '@mui/utils/deepmerge';\nimport defaultSxConfig from './defaultSxConfig';\nconst splitProps = props => {\n  var _props$theme$unstable, _props$theme;\n  const result = {\n    systemProps: {},\n    otherProps: {}\n  };\n  const config = (_props$theme$unstable = props == null || (_props$theme = props.theme) == null ? void 0 : _props$theme.unstable_sxConfig) != null ? _props$theme$unstable : defaultSxConfig;\n  Object.keys(props).forEach(prop => {\n    if (config[prop]) {\n      result.systemProps[prop] = props[prop];\n    } else {\n      result.otherProps[prop] = props[prop];\n    }\n  });\n  return result;\n};\nexport default function extendSxProp(props) {\n  const {\n      sx: inSx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    systemProps,\n    otherProps\n  } = splitProps(other);\n  let finalSx;\n  if (Array.isArray(inSx)) {\n    finalSx = [systemProps, ...inSx];\n  } else if (typeof inSx === 'function') {\n    finalSx = (...args) => {\n      const result = inSx(...args);\n      if (!isPlainObject(result)) {\n        return systemProps;\n      }\n      return _extends({}, systemProps, result);\n    };\n  } else {\n    finalSx = _extends({}, systemProps, inSx);\n  }\n  return _extends({}, otherProps, {\n    sx: finalSx\n  });\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "isPlainObject", "defaultSxConfig", "splitProps", "props", "_props$theme$unstable", "_props$theme", "result", "systemProps", "otherProps", "config", "theme", "unstable_sxConfig", "Object", "keys", "for<PERSON>ach", "prop", "extendSxProp", "sx", "inSx", "other", "finalSx", "Array", "isArray", "args"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/system/esm/styleFunctionSx/extendSxProp.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"sx\"];\nimport { isPlainObject } from '@mui/utils/deepmerge';\nimport defaultSxConfig from './defaultSxConfig';\nconst splitProps = props => {\n  var _props$theme$unstable, _props$theme;\n  const result = {\n    systemProps: {},\n    otherProps: {}\n  };\n  const config = (_props$theme$unstable = props == null || (_props$theme = props.theme) == null ? void 0 : _props$theme.unstable_sxConfig) != null ? _props$theme$unstable : defaultSxConfig;\n  Object.keys(props).forEach(prop => {\n    if (config[prop]) {\n      result.systemProps[prop] = props[prop];\n    } else {\n      result.otherProps[prop] = props[prop];\n    }\n  });\n  return result;\n};\nexport default function extendSxProp(props) {\n  const {\n      sx: inSx\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    systemProps,\n    otherProps\n  } = splitProps(other);\n  let finalSx;\n  if (Array.isArray(inSx)) {\n    finalSx = [systemProps, ...inSx];\n  } else if (typeof inSx === 'function') {\n    finalSx = (...args) => {\n      const result = inSx(...args);\n      if (!isPlainObject(result)) {\n        return systemProps;\n      }\n      return _extends({}, systemProps, result);\n    };\n  } else {\n    finalSx = _extends({}, systemProps, inSx);\n  }\n  return _extends({}, otherProps, {\n    sx: finalSx\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,IAAI,CAAC;AACxB,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,MAAMC,UAAU,GAAGC,KAAK,IAAI;EAC1B,IAAIC,qBAAqB,EAAEC,YAAY;EACvC,MAAMC,MAAM,GAAG;IACbC,WAAW,EAAE,CAAC,CAAC;IACfC,UAAU,EAAE,CAAC;EACf,CAAC;EACD,MAAMC,MAAM,GAAG,CAACL,qBAAqB,GAAGD,KAAK,IAAI,IAAI,IAAI,CAACE,YAAY,GAAGF,KAAK,CAACO,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,YAAY,CAACM,iBAAiB,KAAK,IAAI,GAAGP,qBAAqB,GAAGH,eAAe;EAC1LW,MAAM,CAACC,IAAI,CAACV,KAAK,CAAC,CAACW,OAAO,CAACC,IAAI,IAAI;IACjC,IAAIN,MAAM,CAACM,IAAI,CAAC,EAAE;MAChBT,MAAM,CAACC,WAAW,CAACQ,IAAI,CAAC,GAAGZ,KAAK,CAACY,IAAI,CAAC;IACxC,CAAC,MAAM;MACLT,MAAM,CAACE,UAAU,CAACO,IAAI,CAAC,GAAGZ,KAAK,CAACY,IAAI,CAAC;IACvC;EACF,CAAC,CAAC;EACF,OAAOT,MAAM;AACf,CAAC;AACD,eAAe,SAASU,YAAYA,CAACb,KAAK,EAAE;EAC1C,MAAM;MACFc,EAAE,EAAEC;IACN,CAAC,GAAGf,KAAK;IACTgB,KAAK,GAAGrB,6BAA6B,CAACK,KAAK,EAAEJ,SAAS,CAAC;EACzD,MAAM;IACJQ,WAAW;IACXC;EACF,CAAC,GAAGN,UAAU,CAACiB,KAAK,CAAC;EACrB,IAAIC,OAAO;EACX,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IACvBE,OAAO,GAAG,CAACb,WAAW,EAAE,GAAGW,IAAI,CAAC;EAClC,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IACrCE,OAAO,GAAGA,CAAC,GAAGG,IAAI,KAAK;MACrB,MAAMjB,MAAM,GAAGY,IAAI,CAAC,GAAGK,IAAI,CAAC;MAC5B,IAAI,CAACvB,aAAa,CAACM,MAAM,CAAC,EAAE;QAC1B,OAAOC,WAAW;MACpB;MACA,OAAOV,QAAQ,CAAC,CAAC,CAAC,EAAEU,WAAW,EAAED,MAAM,CAAC;IAC1C,CAAC;EACH,CAAC,MAAM;IACLc,OAAO,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEU,WAAW,EAAEW,IAAI,CAAC;EAC3C;EACA,OAAOrB,QAAQ,CAAC,CAAC,CAAC,EAAEW,UAAU,EAAE;IAC9BS,EAAE,EAAEG;EACN,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}