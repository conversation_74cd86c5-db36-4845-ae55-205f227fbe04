{"ast": null, "code": "'use client';\n\nexport { default } from './GlobalStyles';\nexport * from './GlobalStyles';", "map": {"version": 3, "names": ["default"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/system/esm/GlobalStyles/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './GlobalStyles';\nexport * from './GlobalStyles';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,gBAAgB;AACxC,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}