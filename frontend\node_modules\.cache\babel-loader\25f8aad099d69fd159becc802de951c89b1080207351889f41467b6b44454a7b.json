{"ast": null, "code": "var _jsxFileName = \"D:\\\\project\\\\HNrealstate\\\\frontend\\\\src\\\\pages\\\\Properties.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { fetchProperties } from '../store/slices/propertySlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Properties = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    properties,\n    loading,\n    error\n  } = useSelector(state => state.properties);\n\n  // Filter states\n  const [activeTab, setActiveTab] = useState('sale');\n  const [filters, setFilters] = useState({\n    location: 'all',\n    propertyType: 'all',\n    bedrooms: 'all',\n    priceRange: 'all',\n    sortBy: 'newest'\n  });\n  const [viewMode, setViewMode] = useState('grid');\n\n  // Enhanced property data with rent, sale, and off-plan categories\n  const allProperties = [{\n    id: 1,\n    title: \"Lusail Marina Heights\",\n    location: \"Lusail, Qatar\",\n    price: \"From $850,000\",\n    type: \"Luxury Waterfront Residences\",\n    bedrooms: \"2-4\",\n    bathrooms: \"2-4\",\n    description: \"Smart waterfront luxury residences with panoramic marina views and world-class amenities.\",\n    features: [\"Marina Views\", \"Smart Home Technology\", \"Concierge Service\", \"Private Beach Access\"]\n  }, {\n    id: 2,\n    title: \"Porto Arabia Penthouses\",\n    location: \"The Pearl, Qatar\",\n    price: \"From $1,200,000\",\n    type: \"Exclusive Sea-View Apartments\",\n    bedrooms: \"3-5\",\n    bathrooms: \"3-5\",\n    description: \"Exclusive sea-view apartments and penthouses in the prestigious Pearl development.\",\n    features: [\"Sea Views\", \"Premium Finishes\", \"Private Elevator\", \"Rooftop Terrace\"]\n  }, {\n    id: 3,\n    title: \"Palm Jumeirah Towers\",\n    location: \"Dubai, UAE\",\n    price: \"From $950,000\",\n    type: \"Beachfront Residences\",\n    bedrooms: \"1-4\",\n    bathrooms: \"1-4\",\n    description: \"Iconic beachfront residences with world-class amenities and stunning Arabian Gulf views.\",\n    features: [\"Beach Access\", \"Infinity Pool\", \"Spa & Wellness\", \"24/7 Security\"]\n  }, {\n    id: 4,\n    title: \"New Cairo Villas\",\n    location: \"New Cairo, Egypt\",\n    price: \"From $450,000\",\n    type: \"Premium Gated Community\",\n    bedrooms: \"4-6\",\n    bathrooms: \"4-6\",\n    description: \"Premium gated communities with modern design and comprehensive amenities.\",\n    features: [\"Gated Community\", \"Golf Course\", \"International Schools\", \"Shopping Centers\"]\n  }, {\n    id: 5,\n    title: \"Paris Haussmann Apartments\",\n    location: \"Paris, France\",\n    price: \"From $1,800,000\",\n    type: \"Historic Luxury Apartments\",\n    bedrooms: \"2-5\",\n    bathrooms: \"2-4\",\n    description: \"Historic charm blended with modern luxury in the heart of Paris.\",\n    features: [\"Historic Architecture\", \"City Center Location\", \"Luxury Renovation\", \"High Ceilings\"]\n  }, {\n    id: 6,\n    title: \"Marrakech Retreats\",\n    location: \"Marrakech, Morocco\",\n    price: \"From $650,000\",\n    type: \"Private Villas\",\n    bedrooms: \"3-7\",\n    bathrooms: \"3-6\",\n    description: \"Private villas with authentic Moroccan architecture and modern amenities.\",\n    features: [\"Traditional Design\", \"Private Pool\", \"Garden Courtyard\", \"Mountain Views\"]\n  }, {\n    id: 7,\n    title: \"Istanbul Waterfront Homes\",\n    location: \"Istanbul, Turkey\",\n    price: \"From $380,000\",\n    type: \"Investment Apartments\",\n    bedrooms: \"1-3\",\n    bathrooms: \"1-2\",\n    description: \"Investment-driven apartments with lifestyle appeal and Bosphorus views.\",\n    features: [\"Waterfront Location\", \"High ROI Potential\", \"Modern Amenities\", \"Transport Links\"]\n  }, {\n    id: 8,\n    title: \"Riyadh Business District\",\n    location: \"Riyadh, Saudi Arabia\",\n    price: \"From $720,000\",\n    type: \"Commercial & Residential\",\n    bedrooms: \"2-4\",\n    bathrooms: \"2-3\",\n    description: \"Mixed-use development in the heart of Riyadh's new business district.\",\n    features: [\"Business District\", \"Mixed-Use\", \"Metro Access\", \"Investment Opportunity\"]\n  }];\n  useEffect(() => {\n    dispatch(fetchProperties());\n  }, [dispatch]);\n  const filteredProjects = filter === 'all' ? flagshipProjects : flagshipProjects.filter(project => project.location.toLowerCase().includes(filter.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"properties-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Premium Properties\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"tagline\",\n          children: \"Discover Exceptional Real Estate Opportunities\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Explore our curated selection of luxury properties across Qatar, the Gulf, MENA, and Europe. Each property represents the pinnacle of quality and investment potential.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section section-light\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Flagship Projects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Our premium property portfolio across international markets\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-buttons\",\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            gap: '1rem',\n            marginBottom: '3rem',\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `btn ${filter === 'all' ? 'btn-primary' : 'btn-secondary'}`,\n            onClick: () => setFilter('all'),\n            style: {\n              minWidth: 'auto',\n              padding: '0.7rem 1.5rem'\n            },\n            children: \"All Properties\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `btn ${filter === 'qatar' ? 'btn-primary' : 'btn-secondary'}`,\n            onClick: () => setFilter('qatar'),\n            style: {\n              minWidth: 'auto',\n              padding: '0.7rem 1.5rem'\n            },\n            children: \"Qatar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `btn ${filter === 'uae' ? 'btn-primary' : 'btn-secondary'}`,\n            onClick: () => setFilter('uae'),\n            style: {\n              minWidth: 'auto',\n              padding: '0.7rem 1.5rem'\n            },\n            children: \"UAE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `btn ${filter === 'egypt' ? 'btn-primary' : 'btn-secondary'}`,\n            onClick: () => setFilter('egypt'),\n            style: {\n              minWidth: 'auto',\n              padding: '0.7rem 1.5rem'\n            },\n            children: \"Egypt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `btn ${filter === 'france' ? 'btn-primary' : 'btn-secondary'}`,\n            onClick: () => setFilter('france'),\n            style: {\n              minWidth: 'auto',\n              padding: '0.7rem 1.5rem'\n            },\n            children: \"Europe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"properties-grid\",\n          children: filteredProjects.map(project => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"property-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"property-image\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '1rem',\n                  right: '1rem',\n                  background: 'var(--matte-gold)',\n                  color: 'var(--luxury-burgundy)',\n                  padding: '0.5rem 1rem',\n                  borderRadius: '4px',\n                  fontWeight: '600',\n                  fontSize: '0.9rem'\n                },\n                children: project.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"property-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: project.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-location\",\n                children: [\"\\uD83D\\uDCCD \", project.location]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-price\",\n                children: project.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDECF\\uFE0F \", project.bedrooms, \" bed\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDEBF \", project.bathrooms, \" bath\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: 'var(--text-light)',\n                  marginBottom: '1.5rem',\n                  lineHeight: '1.6'\n                },\n                children: project.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"property-features\",\n                style: {\n                  marginBottom: '1.5rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: '0.5rem'\n                  },\n                  children: project.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      background: 'var(--off-white)',\n                      color: 'var(--luxury-burgundy)',\n                      padding: '0.3rem 0.8rem',\n                      borderRadius: '20px',\n                      fontSize: '0.85rem',\n                      border: '1px solid var(--border-light)'\n                    },\n                    children: feature\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary\",\n                  style: {\n                    flex: 1,\n                    padding: '0.8rem',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"View Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-secondary\",\n                  style: {\n                    flex: 1,\n                    padding: '0.8rem',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Schedule Tour\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, project.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading additional properties...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Error loading properties: \", error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s(Properties, \"WBIxMr+uu086+KNUOX8GsIqDuxs=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = Properties;\nexport default Properties;\nvar _c;\n$RefreshReg$(_c, \"Properties\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "fetchProperties", "jsxDEV", "_jsxDEV", "Properties", "_s", "dispatch", "properties", "loading", "error", "state", "activeTab", "setActiveTab", "filters", "setFilters", "location", "propertyType", "bedrooms", "priceRange", "sortBy", "viewMode", "setViewMode", "allProperties", "id", "title", "price", "type", "bathrooms", "description", "features", "filteredProjects", "filter", "flagshipProjects", "project", "toLowerCase", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "gap", "marginBottom", "flexWrap", "onClick", "setFilter", "min<PERSON><PERSON><PERSON>", "padding", "map", "position", "top", "right", "background", "color", "borderRadius", "fontWeight", "fontSize", "lineHeight", "feature", "index", "border", "flex", "_c", "$RefreshReg$"], "sources": ["D:/project/HNrealstate/frontend/src/pages/Properties.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '../store/store';\nimport { fetchProperties } from '../store/slices/propertySlice';\n\nconst Properties: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { properties, loading, error } = useSelector((state: RootState) => state.properties);\n\n  // Filter states\n  const [activeTab, setActiveTab] = useState<'sale' | 'rent' | 'off-plan'>('sale');\n  const [filters, setFilters] = useState({\n    location: 'all',\n    propertyType: 'all',\n    bedrooms: 'all',\n    priceRange: 'all',\n    sortBy: 'newest'\n  });\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n\n  // Enhanced property data with rent, sale, and off-plan categories\n  const allProperties = [\n    {\n      id: 1,\n      title: \"Lusail Marina Heights\",\n      location: \"Lusail, Qatar\",\n      price: \"From $850,000\",\n      type: \"Luxury Waterfront Residences\",\n      bedrooms: \"2-4\",\n      bathrooms: \"2-4\",\n      description: \"Smart waterfront luxury residences with panoramic marina views and world-class amenities.\",\n      features: [\"Marina Views\", \"Smart Home Technology\", \"Concierge Service\", \"Private Beach Access\"]\n    },\n    {\n      id: 2,\n      title: \"Porto Arabia Penthouses\",\n      location: \"The Pearl, Qatar\",\n      price: \"From $1,200,000\",\n      type: \"Exclusive Sea-View Apartments\",\n      bedrooms: \"3-5\",\n      bathrooms: \"3-5\",\n      description: \"Exclusive sea-view apartments and penthouses in the prestigious Pearl development.\",\n      features: [\"Sea Views\", \"Premium Finishes\", \"Private Elevator\", \"Rooftop Terrace\"]\n    },\n    {\n      id: 3,\n      title: \"Palm Jumeirah Towers\",\n      location: \"Dubai, UAE\",\n      price: \"From $950,000\",\n      type: \"Beachfront Residences\",\n      bedrooms: \"1-4\",\n      bathrooms: \"1-4\",\n      description: \"Iconic beachfront residences with world-class amenities and stunning Arabian Gulf views.\",\n      features: [\"Beach Access\", \"Infinity Pool\", \"Spa & Wellness\", \"24/7 Security\"]\n    },\n    {\n      id: 4,\n      title: \"New Cairo Villas\",\n      location: \"New Cairo, Egypt\",\n      price: \"From $450,000\",\n      type: \"Premium Gated Community\",\n      bedrooms: \"4-6\",\n      bathrooms: \"4-6\",\n      description: \"Premium gated communities with modern design and comprehensive amenities.\",\n      features: [\"Gated Community\", \"Golf Course\", \"International Schools\", \"Shopping Centers\"]\n    },\n    {\n      id: 5,\n      title: \"Paris Haussmann Apartments\",\n      location: \"Paris, France\",\n      price: \"From $1,800,000\",\n      type: \"Historic Luxury Apartments\",\n      bedrooms: \"2-5\",\n      bathrooms: \"2-4\",\n      description: \"Historic charm blended with modern luxury in the heart of Paris.\",\n      features: [\"Historic Architecture\", \"City Center Location\", \"Luxury Renovation\", \"High Ceilings\"]\n    },\n    {\n      id: 6,\n      title: \"Marrakech Retreats\",\n      location: \"Marrakech, Morocco\",\n      price: \"From $650,000\",\n      type: \"Private Villas\",\n      bedrooms: \"3-7\",\n      bathrooms: \"3-6\",\n      description: \"Private villas with authentic Moroccan architecture and modern amenities.\",\n      features: [\"Traditional Design\", \"Private Pool\", \"Garden Courtyard\", \"Mountain Views\"]\n    },\n    {\n      id: 7,\n      title: \"Istanbul Waterfront Homes\",\n      location: \"Istanbul, Turkey\",\n      price: \"From $380,000\",\n      type: \"Investment Apartments\",\n      bedrooms: \"1-3\",\n      bathrooms: \"1-2\",\n      description: \"Investment-driven apartments with lifestyle appeal and Bosphorus views.\",\n      features: [\"Waterfront Location\", \"High ROI Potential\", \"Modern Amenities\", \"Transport Links\"]\n    },\n    {\n      id: 8,\n      title: \"Riyadh Business District\",\n      location: \"Riyadh, Saudi Arabia\",\n      price: \"From $720,000\",\n      type: \"Commercial & Residential\",\n      bedrooms: \"2-4\",\n      bathrooms: \"2-3\",\n      description: \"Mixed-use development in the heart of Riyadh's new business district.\",\n      features: [\"Business District\", \"Mixed-Use\", \"Metro Access\", \"Investment Opportunity\"]\n    }\n  ];\n\n  useEffect(() => {\n    dispatch(fetchProperties());\n  }, [dispatch]);\n\n  const filteredProjects = filter === 'all'\n    ? flagshipProjects\n    : flagshipProjects.filter(project =>\n        project.location.toLowerCase().includes(filter.toLowerCase())\n      );\n\n  return (\n    <div className=\"properties-page\">\n      {/* Hero Section */}\n      <section className=\"hero\">\n        <div className=\"hero-content\">\n          <h1>Premium Properties</h1>\n          <p className=\"tagline\">Discover Exceptional Real Estate Opportunities</p>\n          <p>\n            Explore our curated selection of luxury properties across Qatar, the Gulf,\n            MENA, and Europe. Each property represents the pinnacle of quality and investment potential.\n          </p>\n        </div>\n      </section>\n\n      {/* Filter Section */}\n      <section className=\"section section-light\">\n        <div className=\"container\">\n          <div className=\"section-header\">\n            <h2>Flagship Projects</h2>\n            <p>Our premium property portfolio across international markets</p>\n          </div>\n\n          <div className=\"filter-buttons\" style={{ display: 'flex', justifyContent: 'center', gap: '1rem', marginBottom: '3rem', flexWrap: 'wrap' }}>\n            <button\n              className={`btn ${filter === 'all' ? 'btn-primary' : 'btn-secondary'}`}\n              onClick={() => setFilter('all')}\n              style={{ minWidth: 'auto', padding: '0.7rem 1.5rem' }}\n            >\n              All Properties\n            </button>\n            <button\n              className={`btn ${filter === 'qatar' ? 'btn-primary' : 'btn-secondary'}`}\n              onClick={() => setFilter('qatar')}\n              style={{ minWidth: 'auto', padding: '0.7rem 1.5rem' }}\n            >\n              Qatar\n            </button>\n            <button\n              className={`btn ${filter === 'uae' ? 'btn-primary' : 'btn-secondary'}`}\n              onClick={() => setFilter('uae')}\n              style={{ minWidth: 'auto', padding: '0.7rem 1.5rem' }}\n            >\n              UAE\n            </button>\n            <button\n              className={`btn ${filter === 'egypt' ? 'btn-primary' : 'btn-secondary'}`}\n              onClick={() => setFilter('egypt')}\n              style={{ minWidth: 'auto', padding: '0.7rem 1.5rem' }}\n            >\n              Egypt\n            </button>\n            <button\n              className={`btn ${filter === 'france' ? 'btn-primary' : 'btn-secondary'}`}\n              onClick={() => setFilter('france')}\n              style={{ minWidth: 'auto', padding: '0.7rem 1.5rem' }}\n            >\n              Europe\n            </button>\n          </div>\n\n          {/* Properties Grid */}\n          <div className=\"properties-grid\">\n            {filteredProjects.map((project) => (\n              <div key={project.id} className=\"property-card\">\n                <div className=\"property-image\">\n                  <div style={{\n                    position: 'absolute',\n                    top: '1rem',\n                    right: '1rem',\n                    background: 'var(--matte-gold)',\n                    color: 'var(--luxury-burgundy)',\n                    padding: '0.5rem 1rem',\n                    borderRadius: '4px',\n                    fontWeight: '600',\n                    fontSize: '0.9rem'\n                  }}>\n                    {project.type}\n                  </div>\n                </div>\n                <div className=\"property-content\">\n                  <h3>{project.title}</h3>\n                  <div className=\"property-location\">\n                    📍 {project.location}\n                  </div>\n                  <div className=\"property-price\">{project.price}</div>\n                  <div className=\"property-details\">\n                    <span>🛏️ {project.bedrooms} bed</span>\n                    <span>🚿 {project.bathrooms} bath</span>\n                  </div>\n                  <p style={{ color: 'var(--text-light)', marginBottom: '1.5rem', lineHeight: '1.6' }}>\n                    {project.description}\n                  </p>\n                  <div className=\"property-features\" style={{ marginBottom: '1.5rem' }}>\n                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>\n                      {project.features.map((feature, index) => (\n                        <span\n                          key={index}\n                          style={{\n                            background: 'var(--off-white)',\n                            color: 'var(--luxury-burgundy)',\n                            padding: '0.3rem 0.8rem',\n                            borderRadius: '20px',\n                            fontSize: '0.85rem',\n                            border: '1px solid var(--border-light)'\n                          }}\n                        >\n                          {feature}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                  <div style={{ display: 'flex', gap: '1rem' }}>\n                    <button className=\"btn btn-primary\" style={{ flex: 1, padding: '0.8rem', fontSize: '0.9rem' }}>\n                      View Details\n                    </button>\n                    <button className=\"btn btn-secondary\" style={{ flex: 1, padding: '0.8rem', fontSize: '0.9rem' }}>\n                      Schedule Tour\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {loading && (\n            <div className=\"loading\">\n              <p>Loading additional properties...</p>\n            </div>\n          )}\n\n          {error && (\n            <div className=\"error\">\n              <p>Error loading properties: {error}</p>\n            </div>\n          )}\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Properties;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,eAAe,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGP,WAAW,CAAc,CAAC;EAC3C,MAAM;IAAEQ,UAAU;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGT,WAAW,CAAEU,KAAgB,IAAKA,KAAK,CAACH,UAAU,CAAC;;EAE1F;EACA,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAA+B,MAAM,CAAC;EAChF,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC;IACrCiB,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,KAAK;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAkB,MAAM,CAAC;;EAEjE;EACA,MAAMwB,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,uBAAuB;IAC9BT,QAAQ,EAAE,eAAe;IACzBU,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,8BAA8B;IACpCT,QAAQ,EAAE,KAAK;IACfU,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,2FAA2F;IACxGC,QAAQ,EAAE,CAAC,cAAc,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,sBAAsB;EACjG,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,yBAAyB;IAChCT,QAAQ,EAAE,kBAAkB;IAC5BU,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE,+BAA+B;IACrCT,QAAQ,EAAE,KAAK;IACfU,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,oFAAoF;IACjGC,QAAQ,EAAE,CAAC,WAAW,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,iBAAiB;EACnF,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,sBAAsB;IAC7BT,QAAQ,EAAE,YAAY;IACtBU,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,uBAAuB;IAC7BT,QAAQ,EAAE,KAAK;IACfU,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,0FAA0F;IACvGC,QAAQ,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe;EAC/E,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,kBAAkB;IACzBT,QAAQ,EAAE,kBAAkB;IAC5BU,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,yBAAyB;IAC/BT,QAAQ,EAAE,KAAK;IACfU,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,2EAA2E;IACxFC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,aAAa,EAAE,uBAAuB,EAAE,kBAAkB;EAC1F,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,4BAA4B;IACnCT,QAAQ,EAAE,eAAe;IACzBU,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE,4BAA4B;IAClCT,QAAQ,EAAE,KAAK;IACfU,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,kEAAkE;IAC/EC,QAAQ,EAAE,CAAC,uBAAuB,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,eAAe;EAClG,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oBAAoB;IAC3BT,QAAQ,EAAE,oBAAoB;IAC9BU,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,gBAAgB;IACtBT,QAAQ,EAAE,KAAK;IACfU,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,2EAA2E;IACxFC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB;EACvF,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,2BAA2B;IAClCT,QAAQ,EAAE,kBAAkB;IAC5BU,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,uBAAuB;IAC7BT,QAAQ,EAAE,KAAK;IACfU,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,yEAAyE;IACtFC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,iBAAiB;EAC/F,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCT,QAAQ,EAAE,sBAAsB;IAChCU,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE,0BAA0B;IAChCT,QAAQ,EAAE,KAAK;IACfU,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,uEAAuE;IACpFC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,WAAW,EAAE,cAAc,EAAE,wBAAwB;EACvF,CAAC,CACF;EAEDhC,SAAS,CAAC,MAAM;IACdS,QAAQ,CAACL,eAAe,CAAC,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACK,QAAQ,CAAC,CAAC;EAEd,MAAMwB,gBAAgB,GAAGC,MAAM,KAAK,KAAK,GACrCC,gBAAgB,GAChBA,gBAAgB,CAACD,MAAM,CAACE,OAAO,IAC7BA,OAAO,CAAClB,QAAQ,CAACmB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAACG,WAAW,CAAC,CAAC,CAC9D,CAAC;EAEL,oBACE/B,OAAA;IAAKiC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAE9BlC,OAAA;MAASiC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACvBlC,OAAA;QAAKiC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BlC,OAAA;UAAAkC,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BtC,OAAA;UAAGiC,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzEtC,OAAA;UAAAkC,QAAA,EAAG;QAGH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVtC,OAAA;MAASiC,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACxClC,OAAA;QAAKiC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBlC,OAAA;UAAKiC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlC,OAAA;YAAAkC,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BtC,OAAA;YAAAkC,QAAA,EAAG;UAA2D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAENtC,OAAA;UAAKiC,SAAS,EAAC,gBAAgB;UAACM,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,GAAG,EAAE,MAAM;YAAEC,YAAY,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACxIlC,OAAA;YACEiC,SAAS,EAAE,OAAOL,MAAM,KAAK,KAAK,GAAG,aAAa,GAAG,eAAe,EAAG;YACvEiB,OAAO,EAAEA,CAAA,KAAMC,SAAS,CAAC,KAAK,CAAE;YAChCP,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAgB,CAAE;YAAAd,QAAA,EACvD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtC,OAAA;YACEiC,SAAS,EAAE,OAAOL,MAAM,KAAK,OAAO,GAAG,aAAa,GAAG,eAAe,EAAG;YACzEiB,OAAO,EAAEA,CAAA,KAAMC,SAAS,CAAC,OAAO,CAAE;YAClCP,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAgB,CAAE;YAAAd,QAAA,EACvD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtC,OAAA;YACEiC,SAAS,EAAE,OAAOL,MAAM,KAAK,KAAK,GAAG,aAAa,GAAG,eAAe,EAAG;YACvEiB,OAAO,EAAEA,CAAA,KAAMC,SAAS,CAAC,KAAK,CAAE;YAChCP,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAgB,CAAE;YAAAd,QAAA,EACvD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtC,OAAA;YACEiC,SAAS,EAAE,OAAOL,MAAM,KAAK,OAAO,GAAG,aAAa,GAAG,eAAe,EAAG;YACzEiB,OAAO,EAAEA,CAAA,KAAMC,SAAS,CAAC,OAAO,CAAE;YAClCP,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAgB,CAAE;YAAAd,QAAA,EACvD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtC,OAAA;YACEiC,SAAS,EAAE,OAAOL,MAAM,KAAK,QAAQ,GAAG,aAAa,GAAG,eAAe,EAAG;YAC1EiB,OAAO,EAAEA,CAAA,KAAMC,SAAS,CAAC,QAAQ,CAAE;YACnCP,KAAK,EAAE;cAAEQ,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAgB,CAAE;YAAAd,QAAA,EACvD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNtC,OAAA;UAAKiC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BP,gBAAgB,CAACsB,GAAG,CAAEnB,OAAO,iBAC5B9B,OAAA;YAAsBiC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC7ClC,OAAA;cAAKiC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BlC,OAAA;gBAAKuC,KAAK,EAAE;kBACVW,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,MAAM;kBACXC,KAAK,EAAE,MAAM;kBACbC,UAAU,EAAE,mBAAmB;kBAC/BC,KAAK,EAAE,wBAAwB;kBAC/BN,OAAO,EAAE,aAAa;kBACtBO,YAAY,EAAE,KAAK;kBACnBC,UAAU,EAAE,KAAK;kBACjBC,QAAQ,EAAE;gBACZ,CAAE;gBAAAvB,QAAA,EACCJ,OAAO,CAACP;cAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtC,OAAA;cAAKiC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BlC,OAAA;gBAAAkC,QAAA,EAAKJ,OAAO,CAACT;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxBtC,OAAA;gBAAKiC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,GAAC,eAC9B,EAACJ,OAAO,CAAClB,QAAQ;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACNtC,OAAA;gBAAKiC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEJ,OAAO,CAACR;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDtC,OAAA;gBAAKiC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlC,OAAA;kBAAAkC,QAAA,GAAM,qBAAI,EAACJ,OAAO,CAAChB,QAAQ,EAAC,MAAI;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvCtC,OAAA;kBAAAkC,QAAA,GAAM,eAAG,EAACJ,OAAO,CAACN,SAAS,EAAC,OAAK;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACNtC,OAAA;gBAAGuC,KAAK,EAAE;kBAAEe,KAAK,EAAE,mBAAmB;kBAAEX,YAAY,EAAE,QAAQ;kBAAEe,UAAU,EAAE;gBAAM,CAAE;gBAAAxB,QAAA,EACjFJ,OAAO,CAACL;cAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACJtC,OAAA;gBAAKiC,SAAS,EAAC,mBAAmB;gBAACM,KAAK,EAAE;kBAAEI,YAAY,EAAE;gBAAS,CAAE;gBAAAT,QAAA,eACnElC,OAAA;kBAAKuC,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEI,QAAQ,EAAE,MAAM;oBAAEF,GAAG,EAAE;kBAAS,CAAE;kBAAAR,QAAA,EAC9DJ,OAAO,CAACJ,QAAQ,CAACuB,GAAG,CAAC,CAACU,OAAO,EAAEC,KAAK,kBACnC5D,OAAA;oBAEEuC,KAAK,EAAE;sBACLc,UAAU,EAAE,kBAAkB;sBAC9BC,KAAK,EAAE,wBAAwB;sBAC/BN,OAAO,EAAE,eAAe;sBACxBO,YAAY,EAAE,MAAM;sBACpBE,QAAQ,EAAE,SAAS;sBACnBI,MAAM,EAAE;oBACV,CAAE;oBAAA3B,QAAA,EAEDyB;kBAAO,GAVHC,KAAK;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAWN,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtC,OAAA;gBAAKuC,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,GAAG,EAAE;gBAAO,CAAE;gBAAAR,QAAA,gBAC3ClC,OAAA;kBAAQiC,SAAS,EAAC,iBAAiB;kBAACM,KAAK,EAAE;oBAAEuB,IAAI,EAAE,CAAC;oBAAEd,OAAO,EAAE,QAAQ;oBAAES,QAAQ,EAAE;kBAAS,CAAE;kBAAAvB,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtC,OAAA;kBAAQiC,SAAS,EAAC,mBAAmB;kBAACM,KAAK,EAAE;oBAAEuB,IAAI,EAAE,CAAC;oBAAEd,OAAO,EAAE,QAAQ;oBAAES,QAAQ,EAAE;kBAAS,CAAE;kBAAAvB,QAAA,EAAC;gBAEjG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAxDER,OAAO,CAACV,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELjC,OAAO,iBACNL,OAAA;UAAKiC,SAAS,EAAC,SAAS;UAAAC,QAAA,eACtBlC,OAAA;YAAAkC,QAAA,EAAG;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACN,EAEAhC,KAAK,iBACJN,OAAA;UAAKiC,SAAS,EAAC,OAAO;UAAAC,QAAA,eACpBlC,OAAA;YAAAkC,QAAA,GAAG,4BAA0B,EAAC5B,KAAK;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACpC,EAAA,CAhQID,UAAoB;EAAA,QACPL,WAAW,EACWC,WAAW;AAAA;AAAAkE,EAAA,GAF9C9D,UAAoB;AAkQ1B,eAAeA,UAAU;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}