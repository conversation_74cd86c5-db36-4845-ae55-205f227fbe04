{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableSortLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiTableSortLabel', slot);\n}\nconst tableSortLabelClasses = generateUtilityClasses('MuiTableSortLabel', ['root', 'active', 'icon', 'iconDirectionDesc', 'iconDirectionAsc']);\nexport default tableSortLabelClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTableSortLabelUtilityClass", "slot", "tableSortLabelClasses"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/TableSortLabel/tableSortLabelClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableSortLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiTableSortLabel', slot);\n}\nconst tableSortLabelClasses = generateUtilityClasses('MuiTableSortLabel', ['root', 'active', 'icon', 'iconDirectionDesc', 'iconDirectionAsc']);\nexport default tableSortLabelClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOF,oBAAoB,CAAC,mBAAmB,EAAEE,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGJ,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;AAC9I,eAAeI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}