{"ast": null, "code": "import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\n\n/**\r\n * Hook factory, which creates a `useReduxContext` hook bound to a given context. This is a low-level\r\n * hook that you should usually not need to call directly.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useReduxContext` hook bound to the specified context.\r\n */\nexport function createReduxContextHook(context = ReactReduxContext) {\n  return function useReduxContext() {\n    const contextValue = useContext(context);\n    if (process.env.NODE_ENV !== 'production' && !contextValue) {\n      throw new Error('could not find react-redux context value; please ensure the component is wrapped in a <Provider>');\n    }\n    return contextValue;\n  };\n}\n/**\r\n * A hook to access the value of the `ReactReduxContext`. This is a low-level\r\n * hook that you should usually not need to call directly.\r\n *\r\n * @returns {any} the value of the `ReactReduxContext`\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useReduxContext } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const { store } = useReduxContext()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport const useReduxContext = /*#__PURE__*/createReduxContextHook();", "map": {"version": 3, "names": ["useContext", "ReactReduxContext", "createReduxContextHook", "context", "useReduxContext", "contextValue", "process", "env", "NODE_ENV", "Error"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/react-redux/es/hooks/useReduxContext.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\n\n/**\r\n * Hook factory, which creates a `useReduxContext` hook bound to a given context. This is a low-level\r\n * hook that you should usually not need to call directly.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useReduxContext` hook bound to the specified context.\r\n */\nexport function createReduxContextHook(context = ReactReduxContext) {\n  return function useReduxContext() {\n    const contextValue = useContext(context);\n\n    if (process.env.NODE_ENV !== 'production' && !contextValue) {\n      throw new Error('could not find react-redux context value; please ensure the component is wrapped in a <Provider>');\n    }\n\n    return contextValue;\n  };\n}\n/**\r\n * A hook to access the value of the `ReactReduxContext`. This is a low-level\r\n * hook that you should usually not need to call directly.\r\n *\r\n * @returns {any} the value of the `ReactReduxContext`\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useReduxContext } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const { store } = useReduxContext()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport const useReduxContext = /*#__PURE__*/createReduxContextHook();"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,iBAAiB,QAAQ,uBAAuB;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,OAAO,GAAGF,iBAAiB,EAAE;EAClE,OAAO,SAASG,eAAeA,CAAA,EAAG;IAChC,MAAMC,YAAY,GAAGL,UAAU,CAACG,OAAO,CAAC;IAExC,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACH,YAAY,EAAE;MAC1D,MAAM,IAAII,KAAK,CAAC,kGAAkG,CAAC;IACrH;IAEA,OAAOJ,YAAY;EACrB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMD,eAAe,GAAG,aAAaF,sBAAsB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}