{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogContentTextUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogContentText', slot);\n}\nconst dialogContentTextClasses = generateUtilityClasses('MuiDialogContentText', ['root']);\nexport default dialogContentTextClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getDialogContentTextUtilityClass", "slot", "dialogContentTextClasses"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/DialogContentText/dialogContentTextClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogContentTextUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogContentText', slot);\n}\nconst dialogContentTextClasses = generateUtilityClasses('MuiDialogContentText', ['root']);\nexport default dialogContentTextClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,gCAAgCA,CAACC,IAAI,EAAE;EACrD,OAAOF,oBAAoB,CAAC,sBAAsB,EAAEE,IAAI,CAAC;AAC3D;AACA,MAAMC,wBAAwB,GAAGJ,sBAAsB,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC,CAAC;AACzF,eAAeI,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}