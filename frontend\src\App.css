/* N&H Real Estate Color Palette */
:root {
  --luxury-burgundy: #4B0E14;
  --matte-gold: #C5A059;
  --charcoal-gray: #2C2C2C;
  --off-white: #F8F5F0;
  --pure-white: #FFFFFF;
  --text-dark: #333333;
  --text-light: #666666;
  --border-light: #E5E5E5;
}

.App {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: var(--text-dark);
  line-height: 1.6;
}

/* Header Styles */
.header {
  background: linear-gradient(135deg, var(--luxury-burgundy) 0%, #5a1018 100%);
  padding: 0;
  color: var(--pure-white);
  box-shadow: 0 4px 20px rgba(75, 14, 20, 0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
}

.logo {
  font-size: 2rem;
  font-weight: 700;
  color: var(--pure-white);
  text-decoration: none;
  letter-spacing: -0.5px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo::before {
  content: "N&H";
  background: var(--matte-gold);
  color: var(--luxury-burgundy);
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-weight: 800;
  font-size: 1.2rem;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2.5rem;
  margin: 0;
  padding: 0;
  align-items: center;
}

.nav-links a {
  color: var(--pure-white);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  position: relative;
}

.nav-links a:hover {
  color: var(--matte-gold);
  background: rgba(197, 160, 89, 0.1);
}

.nav-links a.active {
  color: var(--matte-gold);
  background: rgba(197, 160, 89, 0.15);
}

/* Main Content */
main {
  min-height: calc(100vh - 120px);
  background: var(--off-white);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--luxury-burgundy) 0%, #5a1018 100%);
  color: var(--pure-white);
  padding: 6rem 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.hero h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  letter-spacing: -1px;
  line-height: 1.2;
}

.hero .tagline {
  font-size: 1.5rem;
  color: var(--matte-gold);
  font-weight: 300;
  margin-bottom: 2rem;
  font-style: italic;
}

.hero p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto 3rem;
  opacity: 0.9;
  line-height: 1.7;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: var(--matte-gold);
  color: var(--luxury-burgundy);
}

.btn-primary:hover {
  background: #d4b366;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(197, 160, 89, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--pure-white);
  border: 2px solid var(--pure-white);
}

.btn-secondary:hover {
  background: var(--pure-white);
  color: var(--luxury-burgundy);
  transform: translateY(-2px);
}

/* Section Styles */
.section {
  padding: 5rem 2rem;
}

.section-dark {
  background: var(--charcoal-gray);
  color: var(--pure-white);
}

.section-light {
  background: var(--pure-white);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--luxury-burgundy);
}

.section-dark .section-header h2 {
  color: var(--matte-gold);
}

.section-header p {
  font-size: 1.2rem;
  color: var(--text-light);
  max-width: 800px;
  margin: 0 auto;
}

.section-dark .section-header p {
  color: rgba(255, 255, 255, 0.8);
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.service-card {
  background: var(--pure-white);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--matte-gold), var(--luxury-burgundy));
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(75, 14, 20, 0.15);
}

.service-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--luxury-burgundy);
  margin-bottom: 1rem;
}

.service-card p {
  color: var(--text-light);
  line-height: 1.7;
}

/* Properties Grid */
.properties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.property-card {
  background: var(--pure-white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid var(--border-light);
  position: relative;
}

.property-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(75, 14, 20, 0.12);
}

/* Property List View */
.properties-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
}

.property-list-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(75, 14, 20, 0.12);
}

/* Property Tabs */
.property-tabs {
  border-bottom: 2px solid var(--border-light);
  margin-bottom: 2rem;
}

.tab-button {
  padding: 1rem 2rem;
  border: none;
  background: transparent;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-button:hover {
  color: var(--luxury-burgundy);
}

.tab-button.active {
  color: var(--luxury-burgundy);
  border-bottom: 3px solid var(--matte-gold);
}

/* Filter Bar */
.filters-bar {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--pure-white);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  flex-wrap: wrap;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.filter-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--luxury-burgundy);
  margin-bottom: 0.5rem;
}

.filter-group select {
  padding: 0.7rem;
  border: 2px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.95rem;
  background: var(--pure-white);
  transition: border-color 0.3s ease;
}

.filter-group select:focus {
  outline: none;
  border-color: var(--matte-gold);
}

/* View Toggle */
.view-toggle {
  display: flex;
  margin-left: auto;
}

.view-toggle button {
  padding: 0.7rem 1rem;
  border: 2px solid var(--border-light);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.view-toggle button:first-child {
  border-radius: 6px 0 0 6px;
}

.view-toggle button:last-child {
  border-radius: 0 6px 6px 0;
  border-left: none;
}

.view-toggle button.active {
  background: var(--matte-gold);
  color: var(--luxury-burgundy);
  border-color: var(--matte-gold);
}

/* Results Summary */
.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-light);
}

.property-image {
  height: 250px;
  background: linear-gradient(135deg, var(--luxury-burgundy), var(--matte-gold));
  position: relative;
  overflow: hidden;
}

.property-image::after {
  content: 'Premium Property';
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: var(--pure-white);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
}

.property-content {
  padding: 2rem;
}

.property-card h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--luxury-burgundy);
  margin-bottom: 0.5rem;
}

.property-location {
  color: var(--text-light);
  font-size: 1rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.property-price {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--matte-gold);
  margin-bottom: 1rem;
}

.property-details {
  display: flex;
  gap: 1.5rem;
  color: var(--text-light);
  font-size: 0.95rem;
  margin-bottom: 1.5rem;
}

.property-details span {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

/* Stats Section */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.stat-card {
  text-align: center;
  padding: 2rem;
  background: rgba(197, 160, 89, 0.1);
  border-radius: 12px;
  border: 2px solid var(--matte-gold);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--matte-gold);
  display: block;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.1rem;
  color: var(--pure-white);
  font-weight: 500;
}

/* Values Grid */
.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.value-card {
  text-align: center;
  padding: 2.5rem 2rem;
  background: var(--pure-white);
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(75, 14, 20, 0.15);
}

.value-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--matte-gold), #d4b366);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: var(--luxury-burgundy);
}

.value-card h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--luxury-burgundy);
  margin-bottom: 1rem;
}

.value-card p {
  color: var(--text-light);
  line-height: 1.6;
}

/* Footer Styles */
.footer {
  background: var(--luxury-burgundy);
  color: var(--pure-white);
  padding: 4rem 2rem 2rem;
  margin-top: 0;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-section h3 {
  color: var(--matte-gold);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.footer-section p,
.footer-section a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  line-height: 1.7;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--matte-gold);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 0.8rem;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.social-links {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.social-links a {
  width: 40px;
  height: 40px;
  background: rgba(197, 160, 89, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--matte-gold);
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: var(--matte-gold);
  color: var(--luxury-burgundy);
  transform: translateY(-2px);
}

/* Testimonials */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.testimonial-card {
  background: var(--pure-white);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 2rem;
  font-size: 4rem;
  color: var(--matte-gold);
  font-family: serif;
}

.testimonial-text {
  font-style: italic;
  color: var(--text-dark);
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.testimonial-author {
  font-weight: 600;
  color: var(--luxury-burgundy);
}

.testimonial-role {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Contact Section */
.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-top: 3rem;
  align-items: start;
}

.contact-info h3 {
  color: var(--luxury-burgundy);
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: var(--matte-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--luxury-burgundy);
  font-size: 1.2rem;
}

.contact-form {
  background: var(--pure-white);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--luxury-burgundy);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-light);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--matte-gold);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-gold {
  color: var(--matte-gold);
}

.text-burgundy {
  color: var(--luxury-burgundy);
}

.bg-gold {
  background: var(--matte-gold);
}

.bg-burgundy {
  background: var(--luxury-burgundy);
}

.mb-2 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 2rem;
}

.mt-4 {
  margin-top: 2rem;
}

.p-4 {
  padding: 2rem;
}

/* Loading States */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem;
  color: var(--text-light);
}

.error {
  background: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #fcc;
  margin: 1rem 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container {
    max-width: 100%;
    padding: 0 1rem;
  }

  .navbar {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .hero h1 {
    font-size: 2.5rem;
  }

  .hero .tagline {
    font-size: 1.2rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .services-grid,
  .properties-grid {
    grid-template-columns: 1fr;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .nav-links {
    display: none;
  }

  .navbar {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .footer-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  /* Property-specific mobile styles */
  .property-tabs {
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .property-tabs::-webkit-scrollbar {
    display: none;
  }

  .tab-button {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    display: inline-block;
  }

  .filters-bar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .filter-group {
    width: 100%;
  }

  .view-toggle {
    margin-left: 0;
    width: 100%;
  }

  .view-toggle button {
    flex: 1;
  }

  .results-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .property-list-item {
    flex-direction: column;
  }

  .list-property-image {
    width: 100% !important;
    height: 200px !important;
  }

  .list-property-content {
    padding: 1rem !important;
  }

  .property-details {
    flex-direction: column;
    gap: 0.5rem !important;
    align-items: flex-start;
  }

  .property-details span {
    display: block;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 4rem 1rem;
  }

  .section {
    padding: 3rem 1rem;
  }

  .service-card,
  .property-content,
  .contact-form {
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .property-details {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}