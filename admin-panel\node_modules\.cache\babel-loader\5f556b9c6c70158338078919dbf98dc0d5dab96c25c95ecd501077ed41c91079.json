{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getTableContainerUtilityClass } from './tableContainerClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTableContainerUtilityClass, classes);\n};\nconst TableContainerRoot = styled('div', {\n  name: 'MuiTableContainer',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  width: '100%',\n  overflowX: 'auto'\n});\nconst TableContainer = /*#__PURE__*/React.forwardRef(function TableContainer(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTableContainer'\n  });\n  const {\n      className,\n      component = 'div'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TableContainerRoot, _extends({\n    ref: ref,\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TableContainer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `Table`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TableContainer;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "useDefaultProps", "styled", "getTableContainerUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "TableContainerRoot", "name", "slot", "overridesResolver", "props", "styles", "width", "overflowX", "TableContainer", "forwardRef", "inProps", "ref", "className", "component", "other", "as", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "elementType", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/TableContainer/TableContainer.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getTableContainerUtilityClass } from './tableContainerClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTableContainerUtilityClass, classes);\n};\nconst TableContainerRoot = styled('div', {\n  name: 'MuiTableContainer',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  width: '100%',\n  overflowX: 'auto'\n});\nconst TableContainer = /*#__PURE__*/React.forwardRef(function TableContainer(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTableContainer'\n  });\n  const {\n      className,\n      component = 'div'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TableContainerRoot, _extends({\n    ref: ref,\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? TableContainer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `Table`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TableContainer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC;AAC5C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOV,cAAc,CAACS,KAAK,EAAEN,6BAA6B,EAAEK,OAAO,CAAC;AACtE,CAAC;AACD,MAAMG,kBAAkB,GAAGT,MAAM,CAAC,KAAK,EAAE;EACvCU,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDO,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,cAAc,GAAG,aAAatB,KAAK,CAACuB,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAMP,KAAK,GAAGd,eAAe,CAAC;IAC5Bc,KAAK,EAAEM,OAAO;IACdT,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFW,SAAS;MACTC,SAAS,GAAG;IACd,CAAC,GAAGT,KAAK;IACTU,KAAK,GAAG9B,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACzD,MAAMW,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IACrCS;EACF,CAAC,CAAC;EACF,MAAMhB,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACM,kBAAkB,EAAEjB,QAAQ,CAAC;IACpD4B,GAAG,EAAEA,GAAG;IACRI,EAAE,EAAEF,SAAS;IACbD,SAAS,EAAExB,IAAI,CAACS,OAAO,CAACE,IAAI,EAAEa,SAAS,CAAC;IACxChB,UAAU,EAAEA;EACd,CAAC,EAAEkB,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,cAAc,CAACW,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEjC,SAAS,CAACkC,IAAI;EACxB;AACF;AACA;EACExB,OAAO,EAAEV,SAAS,CAACmC,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAEzB,SAAS,CAACoC,MAAM;EAC3B;AACF;AACA;AACA;EACEV,SAAS,EAAE1B,SAAS,CAACqC,WAAW;EAChC;AACF;AACA;EACEC,EAAE,EAAEtC,SAAS,CAACuC,SAAS,CAAC,CAACvC,SAAS,CAACwC,OAAO,CAACxC,SAAS,CAACuC,SAAS,CAAC,CAACvC,SAAS,CAACyC,IAAI,EAAEzC,SAAS,CAACmC,MAAM,EAAEnC,SAAS,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE1C,SAAS,CAACyC,IAAI,EAAEzC,SAAS,CAACmC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAed,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}