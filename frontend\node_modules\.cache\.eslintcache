[{"D:\\project\\HNrealstate\\frontend\\src\\index.tsx": "1", "D:\\project\\HNrealstate\\frontend\\src\\App.tsx": "2", "D:\\project\\HNrealstate\\frontend\\src\\store\\store.ts": "3", "D:\\project\\HNrealstate\\frontend\\src\\pages\\About.tsx": "4", "D:\\project\\HNrealstate\\frontend\\src\\pages\\Home.tsx": "5", "D:\\project\\HNrealstate\\frontend\\src\\pages\\PropertyDetail.tsx": "6", "D:\\project\\HNrealstate\\frontend\\src\\pages\\Contact.tsx": "7", "D:\\project\\HNrealstate\\frontend\\src\\pages\\Properties.tsx": "8", "D:\\project\\HNrealstate\\frontend\\src\\components\\Footer.tsx": "9", "D:\\project\\HNrealstate\\frontend\\src\\components\\Header.tsx": "10", "D:\\project\\HNrealstate\\frontend\\src\\pages\\Services.tsx": "11", "D:\\project\\HNrealstate\\frontend\\src\\store\\slices\\propertySlice.ts": "12", "D:\\project\\HNrealstate\\frontend\\src\\store\\slices\\userSlice.ts": "13"}, {"size": 273, "mtime": 1759580249933, "results": "14", "hashOfConfig": "15"}, {"size": 1207, "mtime": 1759594661062, "results": "16", "hashOfConfig": "15"}, {"size": 377, "mtime": 1759580255265, "results": "17", "hashOfConfig": "15"}, {"size": 11213, "mtime": 1759594509754, "results": "18", "hashOfConfig": "15"}, {"size": 7827, "mtime": 1759594458615, "results": "19", "hashOfConfig": "15"}, {"size": 380, "mtime": 1759580368012, "results": "20", "hashOfConfig": "15"}, {"size": 8081, "mtime": 1759594580177, "results": "21", "hashOfConfig": "15"}, {"size": 36347, "mtime": 1759595768391, "results": "22", "hashOfConfig": "15"}, {"size": 3075, "mtime": 1759594645087, "results": "23", "hashOfConfig": "15"}, {"size": 913, "mtime": 1759594420381, "results": "24", "hashOfConfig": "15"}, {"size": 11430, "mtime": 1759594551261, "results": "25", "hashOfConfig": "15"}, {"size": 2392, "mtime": 1759580265110, "results": "26", "hashOfConfig": "15"}, {"size": 2868, "mtime": 1759580276079, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1v7wstx", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\project\\HNrealstate\\frontend\\src\\index.tsx", [], [], "D:\\project\\HNrealstate\\frontend\\src\\App.tsx", [], [], "D:\\project\\HNrealstate\\frontend\\src\\store\\store.ts", [], [], "D:\\project\\HNrealstate\\frontend\\src\\pages\\About.tsx", [], [], "D:\\project\\HNrealstate\\frontend\\src\\pages\\Home.tsx", [], [], "D:\\project\\HNrealstate\\frontend\\src\\pages\\PropertyDetail.tsx", [], [], "D:\\project\\HNrealstate\\frontend\\src\\pages\\Contact.tsx", [], [], "D:\\project\\HNrealstate\\frontend\\src\\pages\\Properties.tsx", ["67", "68"], [], "D:\\project\\HNrealstate\\frontend\\src\\components\\Footer.tsx", ["69", "70", "71", "72", "73"], [], "D:\\project\\HNrealstate\\frontend\\src\\components\\Header.tsx", [], [], "D:\\project\\HNrealstate\\frontend\\src\\pages\\Services.tsx", [], [], "D:\\project\\HNrealstate\\frontend\\src\\store\\slices\\propertySlice.ts", ["74"], [], "D:\\project\\HNrealstate\\frontend\\src\\store\\slices\\userSlice.ts", ["75"], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 8, "column": 11, "nodeType": "78", "messageId": "79", "endLine": 8, "endColumn": 21}, {"ruleId": "76", "severity": 1, "message": "80", "line": 285, "column": 9, "nodeType": "78", "messageId": "79", "endLine": 285, "endColumn": 20}, {"ruleId": "81", "severity": 1, "message": "82", "line": 69, "column": 13, "nodeType": "83", "endLine": 69, "endColumn": 47}, {"ruleId": "81", "severity": 1, "message": "82", "line": 70, "column": 13, "nodeType": "83", "endLine": 70, "endColumn": 48}, {"ruleId": "81", "severity": 1, "message": "82", "line": 71, "column": 13, "nodeType": "83", "endLine": 71, "endColumn": 46}, {"ruleId": "81", "severity": 1, "message": "82", "line": 72, "column": 13, "nodeType": "83", "endLine": 72, "endColumn": 47}, {"ruleId": "81", "severity": 1, "message": "82", "line": 73, "column": 13, "nodeType": "83", "endLine": 73, "endColumn": 46}, {"ruleId": "76", "severity": 1, "message": "84", "line": 1, "column": 41, "nodeType": "78", "messageId": "79", "endLine": 1, "endColumn": 54}, {"ruleId": "76", "severity": 1, "message": "84", "line": 1, "column": 41, "nodeType": "78", "messageId": "79", "endLine": 1, "endColumn": 54}, "@typescript-eslint/no-unused-vars", "'properties' is assigned a value but never used.", "Identifier", "unusedVar", "'formatPrice' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'PayloadAction' is defined but never used."]