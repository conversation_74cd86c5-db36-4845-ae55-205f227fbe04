{"ast": null, "code": "export { default } from './unsupportedProp';", "map": {"version": 3, "names": ["default"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/utils/esm/unsupportedProp/index.js"], "sourcesContent": ["export { default } from './unsupportedProp';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}