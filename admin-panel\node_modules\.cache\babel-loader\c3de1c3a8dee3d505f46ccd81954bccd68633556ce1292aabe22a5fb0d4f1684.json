{"ast": null, "code": "export const notInitialized = () => {\n  throw new Error('uSES not initialized!');\n};", "map": {"version": 3, "names": ["notInitialized", "Error"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/react-redux/es/utils/useSyncExternalStore.js"], "sourcesContent": ["export const notInitialized = () => {\n  throw new Error('uSES not initialized!');\n};"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAGA,CAAA,KAAM;EAClC,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}