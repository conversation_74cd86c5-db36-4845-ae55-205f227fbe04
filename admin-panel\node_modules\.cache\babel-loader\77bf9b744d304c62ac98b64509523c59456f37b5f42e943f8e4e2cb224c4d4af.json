{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTabsUtilityClass(slot) {\n  return generateUtilityClass('MuiTabs', slot);\n}\nconst tabsClasses = generateUtilityClasses('MuiTabs', ['root', 'vertical', 'flexContainer', 'flexContainerVertical', 'centered', 'scroller', 'fixed', 'scrollableX', 'scrollableY', 'hideScrollbar', 'scrollButtons', 'scrollButtonsHideMobile', 'indicator']);\nexport default tabsClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTabsUtilityClass", "slot", "tabsClasses"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/Tabs/tabsClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTabsUtilityClass(slot) {\n  return generateUtilityClass('MuiTabs', slot);\n}\nconst tabsClasses = generateUtilityClasses('MuiTabs', ['root', 'vertical', 'flexContainer', 'flexContainerVertical', 'centered', 'scroller', 'fixed', 'scrollableX', 'scrollableY', 'hideScrollbar', 'scrollButtons', 'scrollButtonsHideMobile', 'indicator']);\nexport default tabsClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOF,oBAAoB,CAAC,SAAS,EAAEE,IAAI,CAAC;AAC9C;AACA,MAAMC,WAAW,GAAGJ,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,uBAAuB,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,yBAAyB,EAAE,WAAW,CAAC,CAAC;AAC9P,eAAeI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}