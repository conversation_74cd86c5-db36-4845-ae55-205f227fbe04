{"ast": null, "code": "import { getPath } from '@mui/system';\nimport { alpha } from '@mui/system/colorManipulator';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "alpha", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "error", "transformDeprecatedColors", "color", "getTextDecoration", "theme", "ownerState", "transformedColor", "channelColor"], "sources": ["D:/project/HNrealstate/admin-panel/node_modules/@mui/material/Link/getTextDecoration.js"], "sourcesContent": ["import { getPath } from '@mui/system';\nimport { alpha } from '@mui/system/colorManipulator';\nexport const colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst getTextDecoration = ({\n  theme,\n  ownerState\n}) => {\n  const transformedColor = transformDeprecatedColors(ownerState.color);\n  const color = getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;\n  const channelColor = getPath(theme, `palette.${transformedColor}Channel`);\n  if ('vars' in theme && channelColor) {\n    return `rgba(${channelColor} / 0.4)`;\n  }\n  return alpha(color, 0.4);\n};\nexport default getTextDecoration;"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASC,KAAK,QAAQ,8BAA8B;AACpD,OAAO,MAAMC,oBAAoB,GAAG;EAClCC,OAAO,EAAE,cAAc;EACvBC,WAAW,EAAE,cAAc;EAC3BC,SAAS,EAAE,gBAAgB;EAC3BC,aAAa,EAAE,gBAAgB;EAC/BC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,yBAAyB,GAAGC,KAAK,IAAI;EACzC,OAAOP,oBAAoB,CAACO,KAAK,CAAC,IAAIA,KAAK;AAC7C,CAAC;AACD,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAGL,yBAAyB,CAACI,UAAU,CAACH,KAAK,CAAC;EACpE,MAAMA,KAAK,GAAGT,OAAO,CAACW,KAAK,EAAE,WAAWE,gBAAgB,EAAE,EAAE,KAAK,CAAC,IAAID,UAAU,CAACH,KAAK;EACtF,MAAMK,YAAY,GAAGd,OAAO,CAACW,KAAK,EAAE,WAAWE,gBAAgB,SAAS,CAAC;EACzE,IAAI,MAAM,IAAIF,KAAK,IAAIG,YAAY,EAAE;IACnC,OAAO,QAAQA,YAAY,SAAS;EACtC;EACA,OAAOb,KAAK,CAACQ,KAAK,EAAE,GAAG,CAAC;AAC1B,CAAC;AACD,eAAeC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}